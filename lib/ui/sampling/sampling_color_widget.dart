import 'package:flutter/material.dart';

/// 采色界面Widget
class SamplingColorWidget extends StatefulWidget {
  const SamplingColorWidget({super.key});

  @override
  State<SamplingColorWidget> createState() => _SamplingColorWidgetState();
}

class _SamplingColorWidgetState extends State<SamplingColorWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _buttonController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Center(
            child: Container(
              width: 375,
              height: 812,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(40),
                border: Border.all(
                  color: const Color(0x1212112A).withOpacity(0.07),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 30,
                    offset: const Offset(0, 15),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(38),
                child: Column(
                  children: [
                    // 色板区域
                    _buildColorSection(
                      context: context,
                      title: '色板',
                      description: '色板，调试是我的专业!',
                      backgroundColor: const Color(0xFF34A1EB),
                      buttonColor: const Color(0xFF34A1EB),
                      onTap: () => _handleSectionTap('palette'),
                      onButtonTap: () => _handleButtonTap('palette'),
                    ),
                    // 色环区域
                    _buildColorSection(
                      context: context,
                      title: '色环',
                      description: '色环，你的调色老朋友！',
                      backgroundColor: const Color(0xFF9C4AFF),
                      buttonColor: const Color(0xFF9C4AFF),
                      onTap: () => _handleSectionTap('wheel'),
                      onButtonTap: () => _handleButtonTap('wheel'),
                    ),
                    // 色轮区域
                    _buildColorSection(
                      context: context,
                      title: '色轮',
                      description: '色轮，调色如此完美精细！',
                      backgroundColor: const Color(0xFFFF6B6B),
                      buttonColor: const Color(0xFFFF6B6B),
                      onTap: () => _handleSectionTap('picker'),
                      onButtonTap: () => _handleButtonTap('picker'),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建颜色区域
  Widget _buildColorSection({
    required BuildContext context,
    required String title,
    required String description,
    required Color backgroundColor,
    required Color buttonColor,
    required VoidCallback onTap,
    required VoidCallback onButtonTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: backgroundColor,
          ),
          child: Stack(
            children: [
              // 渐变遮罩
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.15),
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
              ),
              // 内容区域
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 标题
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 42,
                        fontWeight: FontWeight.w900,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 15),
                    // 描述
                    SizedBox(
                      width: MediaQuery.of(context).size.width * 0.7,
                      child: Text(
                        description,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.white.withOpacity(0.9),
                          height: 1.6,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // 浮动按钮
              Positioned(
                right: 25,
                bottom: 25,
                child: GestureDetector(
                  onTap: onButtonTap,
                  child: AnimatedBuilder(
                    animation: _buttonController,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: 1.0 + (_buttonController.value * 0.2),
                        child: Transform.rotate(
                          angle: _buttonController.value * 0.785, // 45度
                          child: Container(
                            width: 60,
                            height: 60,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(30),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.15),
                                  blurRadius: 15,
                                  offset: const Offset(0, 6),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Icon(
                                Icons.arrow_forward,
                                color: buttonColor,
                                size: 26,
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理区域点击
  void _handleSectionTap(String section) {
    // 创建点击波纹效果
    _pulseController.forward().then((_) {
      _pulseController.reset();
    });

    // 根据不同区域执行不同操作
    switch (section) {
      case 'palette':
        print('点击了色板区域');
        // TODO: 导航到色板页面
        break;
      case 'wheel':
        print('点击了色环区域');
        // TODO: 导航到色环页面
        break;
      case 'picker':
        print('点击了色轮区域');
        // TODO: 导航到色轮页面
        break;
    }
  }

  /// 处理按钮点击
  void _handleButtonTap(String section) {
    // 按钮动画效果
    _buttonController.forward().then((_) {
      Future.delayed(const Duration(milliseconds: 200), () {
        _buttonController.reverse();
      });
    });

    // 根据不同区域执行不同操作
    switch (section) {
      case 'palette':
        print('点击了色板按钮');
        // TODO: 导航到色板页面
        break;
      case 'wheel':
        print('点击了色环按钮');
        // TODO: 导航到色环页面
        break;
      case 'picker':
        print('点击了色轮按钮');
        // TODO: 导航到色轮页面
        break;
    }
  }
}