import 'package:flutter/material.dart';
import 'package:collect_colors/db/models/color_value.dart';

/// 颜色详情页面Widget - 全屏颜色显示
class ColorDetailWidget extends StatefulWidget {
  final ColorValue colorValue;

  const ColorDetailWidget({
    super.key,
    required this.colorValue,
  });

  @override
  State<ColorDetailWidget> createState() => _ColorDetailWidgetState();
}

class _ColorDetailWidgetState extends State<ColorDetailWidget> {
  bool _isTextVisible = true; // 控制文本显示/隐藏的状态

  /// 将HEX颜色值转换为Color对象
  Color _hexToColor(String hex) {
    String hexCode = hex.replaceAll('#', '');
    if (hexCode.length == 6) {
      hexCode = 'FF$hexCode';
    }
    return Color(int.parse(hexCode, radix: 16));
  }

  /// 切换文本显示状态
  void _toggleTextVisibility() {
    setState(() {
      _isTextVisible = !_isTextVisible;
    });
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _hexToColor(widget.colorValue.hex);

    return Scaffold(
      body: GestureDetector(
        onTap: _toggleTextVisibility,
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: backgroundColor,
          child: SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 返回按钮
                      GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Image.asset(
                              'assets/icons/icon_nav_back.png',
                              width: 24,
                              height: 24,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      // 更多按钮
                      GestureDetector(
                        onTap: () {
                          // TODO: 实现更多功能
                        },
                        child: Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Center(
                            child: Image.asset(
                              'assets/icons/icon_more.png',
                              width: 24,
                              height: 24,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 中间内容区域 - 显示颜色信息
                Expanded(
                  child: Center(
                    child: AnimatedOpacity(
                      opacity: _isTextVisible ? 1.0 : 0.0,
                      duration: const Duration(milliseconds: 300),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // 颜色名称
                          Text(
                            widget.colorValue.name,
                            style: TextStyle(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 20),

                          // 描述文本
                          if (widget.colorValue.description.isNotEmpty)
                            Container(
                              margin: const EdgeInsets.symmetric(horizontal: 40),
                              child: Text(
                                widget.colorValue.description,
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.w400,
                                  color: Colors.white.withOpacity(0.9),
                                  height: 1.5,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.1),
                                      blurRadius: 4,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                textAlign: TextAlign.center,
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),

                          const SizedBox(height: 30),
                          // 提示文本
                          Text(
                            "点击右上角更多按钮，查看更多信息 \n 点击屏幕可隐藏或显示的所有文本",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                              color: Colors.white.withOpacity(0.8),
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
