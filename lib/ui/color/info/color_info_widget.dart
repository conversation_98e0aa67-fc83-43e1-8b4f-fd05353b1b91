import 'package:flutter/material.dart';
import 'package:collect_colors/db/models/color_value.dart';
import 'package:collect_colors/ui/color/detail/color_detail_widget.dart';

/// 颜色详情页面Widget
class ColorInfoWidget extends StatelessWidget {
  final ColorValue colorValue;

  const ColorInfoWidget({
    super.key,
    required this.colorValue,
  });

  /// 将HEX颜色值转换为Color对象
  Color _hexToColor(String hex) {
    String hexCode = hex.replaceAll('#', '');
    if (hexCode.length == 6) {
      hexCode = 'FF$hexCode';
    }
    return Color(int.parse(hexCode, radix: 16));
  }

  @override
  Widget build(BuildContext context) {
    final backgroundColor = _hexToColor(colorValue.hex);

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          // 颜色展示区域
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              color: backgroundColor,
              child: Stack(
                children: [
                  // 返回按钮
                  Positioned(
                    top: 60,
                    left: 16,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.arrow_back_ios,
                            size: 20,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),

                  // 颜色名称
                  Positioned(
                    top: 120,
                    left: 20,
                    right: 20,
                    child: Text(
                      colorValue.name,
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // HEX值
                  Positioned(
                    top: 200,
                    left: 20,
                    right: 20,
                    child: Text(
                      colorValue.hex,
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                        color: Colors.white.withOpacity(0.9),
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // RGB值
                  Positioned(
                    top: 240,
                    left: 20,
                    right: 20,
                    child: Text(
                      colorValue.rgb,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                        color: Colors.white.withOpacity(0.8),
                        shadows: [
                          Shadow(
                            color: Colors.black.withOpacity(0.1),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // 描述文本
                  if (colorValue.description.isNotEmpty)
                    Positioned(
                      bottom: 80,
                      left: 40,
                      right: 40,
                      child: Text(
                        colorValue.description,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w400,
                          color: Colors.white.withOpacity(0.9),
                          height: 1.5,
                          shadows: [
                            Shadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                  // 详情按钮
                  Positioned(
                    bottom: 20,
                    right: 20,
                    child: GestureDetector(
                      onTap: () {
                        // 导航到ColorDetailWidget
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => ColorDetailWidget(colorValue: colorValue),
                          ),
                        );
                      },
                      child: Container(
                        width: 100,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(6),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Center(
                          child: Text(
                            '查看',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFFFFFFFF),
                              shadows: [
                                Shadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 颜色信息区域
          Expanded(
            flex: 2,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '颜色信息',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildPropertyItem('HEX', colorValue.hex),
                  _buildPropertyItem('RGB', colorValue.rgb),
                  _buildPropertyItem('HSL', colorValue.hsb),
                  _buildPropertyItem('CMYK', colorValue.cmyk),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 构建属性项
  Widget _buildPropertyItem(String label, String value) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF666666),
            ),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }
}
