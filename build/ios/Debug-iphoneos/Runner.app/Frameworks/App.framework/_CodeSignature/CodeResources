<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>flutter_assets/AssetManifest.bin</key>
		<data>
		2dl3uKr7RLtKN9HDglJr1yqzUnk=
		</data>
		<key>flutter_assets/AssetManifest.json</key>
		<data>
		STxztQf8PlryljoA8JwV4Qmq/+w=
		</data>
		<key>flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>flutter_assets/NOTICES.Z</key>
		<data>
		8Zk3kTac4PWglLbe6YUB5E5gPxU=
		</data>
		<key>flutter_assets/assets/icons/icon_download.png</key>
		<data>
		ReZMJEmf4XYCwCk8j3qoThmYAwo=
		</data>
		<key>flutter_assets/assets/icons/icon_like.png</key>
		<data>
		zSWky9Bml4ejOh0aWiIAhhs8qnY=
		</data>
		<key>flutter_assets/assets/icons/icon_more.png</key>
		<data>
		w6PWgtQ4VoWRXknzty+fk1JQmoY=
		</data>
		<key>flutter_assets/assets/icons/icon_nav_back.png</key>
		<data>
		h03IvWujya00IWzXdAvn0Hwsdcw=
		</data>
		<key>flutter_assets/assets/icons/icon_unlike.png</key>
		<data>
		soMZHpfaX3rQbGKBQDjOEpKYfFA=
		</data>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>flutter_assets/isolate_snapshot_data</key>
		<data>
		SIm4/1Paq4sWKgTicGS1/n3+2I0=
		</data>
		<key>flutter_assets/kernel_blob.bin</key>
		<data>
		nfbKmI4ItwtQnBTj25LB/1ZTqgk=
		</data>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		hBZgIGdEvhX+IvXd5bjkjJVu6+s=
		</data>
		<key>flutter_assets/vm_snapshot_data</key>
		<data>
		ap40xllbguuMfkV4mJkKh/NQWg4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			57dpbHWrloluavLMDwmTTNlgawAfzmj+Yl3FIV400/U=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8pPe9oz5w8eDYuh8t5U8Z4lrpb7pNS77/3F3c/Gsn5g=
			</data>
		</dict>
		<key>flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			za4tyYBhJ0juxiVhcD5Lsp9dopHa7a7YFkeqJEVP8Qw=
			</data>
		</dict>
		<key>flutter_assets/assets/icons/icon_download.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GQe6GNTB1MG60X7lD+/RX1NMqa1f85R3Wes178RR1II=
			</data>
		</dict>
		<key>flutter_assets/assets/icons/icon_like.png</key>
		<dict>
			<key>hash2</key>
			<data>
			m8QUjoorlK+JQGuPLXCueLNAnnQkNMc4uH0VTh7aK4o=
			</data>
		</dict>
		<key>flutter_assets/assets/icons/icon_more.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qjVeMLNC6hq/0A9pv8fz3fk6OWw/gaesIfC1Y2pMM+Q=
			</data>
		</dict>
		<key>flutter_assets/assets/icons/icon_nav_back.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RecIGmPPFCRLEvQkxm9/9S+NZGXzpbaO/adRGTl7TSY=
			</data>
		</dict>
		<key>flutter_assets/assets/icons/icon_unlike.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7AtoYmIr0fm/DrWMlv/bAgejAqEFqOsPQ4UeVjKQQI8=
			</data>
		</dict>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			anOb2f9bedJ1njfLAJCJBLpvzqbr//Igm8Bh1zQ9Yk4=
			</data>
		</dict>
		<key>flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			fPIXAa0VxK1NbilSux5TNbaNBnvWUxpM5PtlJWyW784=
			</data>
		</dict>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			0T+w183eMSTh5phjUfkXjg4ZpPYnVqkH4Gv3Ge4QCh8=
			</data>
		</dict>
		<key>flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			6eubeZZqyUNzwtBtT1r66GIIirzwERUZET0Gp6oe/CI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
