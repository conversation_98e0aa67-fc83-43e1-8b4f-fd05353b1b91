<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<data>
		mnLbgBhrpRwdlXh4UKzYj73lYuA=
		</data>
		<key><EMAIL></key>
		<data>
		W0gmQxmINA6vbi5M4Y5KotdMnVA=
		</data>
		<key>AppIcon76x76@2x~ipad.png</key>
		<data>
		k2fBD/jokRFQCCj8xCDSOt3s78k=
		</data>
		<key>Assets.car</key>
		<data>
		+r6PcjBFNsd5UXX2Af8SCCYw+YM=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<data>
		28xWMBQ91UzszfdXY91SqhC7ecg=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<data>
		n2t8gsDpfE6XkhG31p7IQJRxTxU=
		</data>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<data>
		ZVgM1+KwZcZnwhgaI0F7Bt1ba2c=
		</data>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<data>
		hMnf/VIyTGR2nRcoLS3JCfeGmDs=
		</data>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<data>
		MDrKFvFWroTb0+KEbQShBcoBvo4=
		</data>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<data>
		nFC1waP0YzYOchnqa85lPwrC73s=
		</data>
		<key>Frameworks/App.framework/App</key>
		<data>
		IjsdK1nPhmoPa3lcnt27Tdt9W6g=
		</data>
		<key>Frameworks/App.framework/Info.plist</key>
		<data>
		h5OB7aKzS5WR9SemvZAyN6FEkJs=
		</data>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<data>
		hYn6rqRzyHjtRH/OX1DHC4opUmg=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<data>
		2dl3uKr7RLtKN9HDglJr1yqzUnk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<data>
		STxztQf8PlryljoA8JwV4Qmq/+w=
		</data>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<data>
		8Zk3kTac4PWglLbe6YUB5E5gPxU=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_download.png</key>
		<data>
		ReZMJEmf4XYCwCk8j3qoThmYAwo=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_like.png</key>
		<data>
		zSWky9Bml4ejOh0aWiIAhhs8qnY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_more.png</key>
		<data>
		w6PWgtQ4VoWRXknzty+fk1JQmoY=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_nav_back.png</key>
		<data>
		h03IvWujya00IWzXdAvn0Hwsdcw=
		</data>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_unlike.png</key>
		<data>
		soMZHpfaX3rQbGKBQDjOEpKYfFA=
		</data>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<data>
		SIm4/1Paq4sWKgTicGS1/n3+2I0=
		</data>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<data>
		nfbKmI4ItwtQnBTj25LB/1ZTqgk=
		</data>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		hBZgIGdEvhX+IvXd5bjkjJVu6+s=
		</data>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<data>
		ap40xllbguuMfkV4mJkKh/NQWg4=
		</data>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<data>
		guuJggRh/WCLvOYdLm1jqi/2krU=
		</data>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<data>
		wTPJHICwW6wxY3b87ek7ITN5kJk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<data>
		zbvYFr9dywry0lMMrHuNOOaNgkY=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<data>
		ksjIMu5IPw+Q3rw2YkAx0KjxkdM=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<data>
		V/wkSSsyYdMoexF6wPrC3KgkL4g=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<data>
		vFsZXNqjflvqKqAzsIptQaTSJho=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<data>
		sUgX1PJzkvyinL5i7nS1ro/Kd5o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<data>
		1Ii7W6lYWZUeB6C6rtn269jAxgA=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<data>
		AqVvCbPmgWMQKrRnib05Okrjbp0=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<data>
		nZcTgHGDD30QzPLlQfP8gP+S06o=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		UqnnVWwQEYYX56eu7lt6dpR3LIc=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<data>
		crQ9782ULebLQfIR+MbBkjB7d+k=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<data>
		ocQVSiAiUMYfVtZIn48LpYTJA5w=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<data>
		EARXud6pHb7ZYP8eXPDnluMqcXk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		qWHw5VIWEa0NmJ1PMhD16nlfRKk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<data>
		7nFruy6bmD9XHvbzEwfdTXE4Rkk=
		</data>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<data>
		n0oqDKaGwBHcAUY4+7F+z78onKc=
		</data>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<data>
		PDU2bz71o1hsfq3mgwdaqF97hEU=
		</data>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<data>
		D+cqXttvC7E/uziGjFdqFabWd7A=
		</data>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<data>
		PQd7oRIIx0HW2ZnuPTGMo2OyWV0=
		</data>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<data>
		Ubat0LvE4LUgCwHeyl0Anx2vnzk=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<data>
		o/KPEuolPRiVJQG68Kfz63fheYk=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<data>
		6FU0bRObpASwDo2wkh1r085V6E8=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<data>
		AFHUFnvg0ZrM3ZAmlxPgBgG8qb0=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<data>
		c/OSPWKX4y0BBLMeI0ALXsf6J6g=
		</data>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		6uLTlq7fgHHWA8emYDf4ImHC+AY=
		</data>
		<key>Frameworks/sqflite.framework/Info.plist</key>
		<data>
		YdSi/I8bFimtx/BBhg8iZtzifyA=
		</data>
		<key>Frameworks/sqflite.framework/_CodeSignature/CodeResources</key>
		<data>
		hnm0L5QqBluouIa9v/GQsGUvUBs=
		</data>
		<key>Frameworks/sqflite.framework/sqflite</key>
		<data>
		uqCUqSDb793ThruQSjrWp7UDIJ4=
		</data>
		<key>Frameworks/sqflite.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<data>
		VmvwwoIp/iFjY11iEx+j12hj7wA=
		</data>
		<key>Frameworks/sqflite.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<data>
		YIiJ5tHvqBeSpBm2mcfVZdaGz3E=
		</data>
		<key>Info.plist</key>
		<data>
		PDYeDBWqJ0WJ2R1FMxbI6hF5ZBo=
		</data>
		<key>PkgInfo</key>
		<data>
		n57qDP4tZfLD1rCS43W0B4LQjzE=
		</data>
		<key>Runner.debug.dylib</key>
		<data>
		WlpbI+KhXdebwC9vqbg+TO8psCA=
		</data>
		<key>__preview.dylib</key>
		<data>
		0IBPhEm9A2TzcHjZIp+G/uLr25E=
		</data>
		<key>embedded.mobileprovision</key>
		<data>
		hmZHlYmlLvx4cUcEHNhhblGyITc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>AppFrameworkInfo.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zb9VR5aeuJMnm/RgXM3cr4LUNi9UZgxKD7xAgkid0NI=
			</data>
		</dict>
		<key><EMAIL></key>
		<dict>
			<key>hash2</key>
			<data>
			nTKNHUXzhjdeKHNhWbT/pTxJMOOWIBbb+YsCmk6AEhw=
			</data>
		</dict>
		<key>AppIcon76x76@2x~ipad.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KaU6BpCNLefiwNS1DZZ8xhrLM/dmicIH5D8o6XJMOZY=
			</data>
		</dict>
		<key>Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			MjI2aPNw5aG1m06zco7vMYDatOw36QOFY6WrcG1OrBU=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/01J-lp-oVM-view-Ze5-6b-2t3.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			by6WshwXWgbEYiAy2bvh0UtjSVa3EwySkNFc1FazGdY=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			HyVdXMU7Ux4/KalAao30mpWOK/lEPT4gvYN09wf31cg=
			</data>
		</dict>
		<key>Base.lproj/LaunchScreen.storyboardc/UIViewController-01J-lp-oVM.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			VPNjf2cf66XxnoLsT0p/tEi7PPwPsYDwiapXH8jwU+I=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/BYZ-38-t0r-view-8bC-Xf-vdC.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BY/hOMO0FcCl8mCMQqjVbFeb8Q97c1G9lHscfspHFNk=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			PpvapAjR62rl6Ym4E6hkTgpKmBICxTaQXeUqcpHmmqQ=
			</data>
		</dict>
		<key>Base.lproj/Main.storyboardc/UIViewController-BYZ-38-t0r.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			y90o2JQjssm+7ysnziyWCNMNbGqdLnZ595pTgURE5T8=
			</data>
		</dict>
		<key>Frameworks/App.framework/App</key>
		<dict>
			<key>hash2</key>
			<data>
			jzHLM1hZ+xIlZm0NDPoQfhbDEF0pQfX4jK1DLHCFBCw=
			</data>
		</dict>
		<key>Frameworks/App.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			SES+IrctFtb2ATzloiQHKMcw7x/vnPQ6XFUZixhhSAI=
			</data>
		</dict>
		<key>Frameworks/App.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			qdZT/v+INnh2Ad57fIugf+wwAzm8OmN12FAhRsiZzNA=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			57dpbHWrloluavLMDwmTTNlgawAfzmj+Yl3FIV400/U=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8pPe9oz5w8eDYuh8t5U8Z4lrpb7pNS77/3F3c/Gsn5g=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			za4tyYBhJ0juxiVhcD5Lsp9dopHa7a7YFkeqJEVP8Qw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_download.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GQe6GNTB1MG60X7lD+/RX1NMqa1f85R3Wes178RR1II=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_like.png</key>
		<dict>
			<key>hash2</key>
			<data>
			m8QUjoorlK+JQGuPLXCueLNAnnQkNMc4uH0VTh7aK4o=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_more.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qjVeMLNC6hq/0A9pv8fz3fk6OWw/gaesIfC1Y2pMM+Q=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_nav_back.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RecIGmPPFCRLEvQkxm9/9S+NZGXzpbaO/adRGTl7TSY=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/assets/icons/icon_unlike.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7AtoYmIr0fm/DrWMlv/bAgejAqEFqOsPQ4UeVjKQQI8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			anOb2f9bedJ1njfLAJCJBLpvzqbr//Igm8Bh1zQ9Yk4=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			fPIXAa0VxK1NbilSux5TNbaNBnvWUxpM5PtlJWyW784=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			0T+w183eMSTh5phjUfkXjg4ZpPYnVqkH4Gv3Ge4QCh8=
			</data>
		</dict>
		<key>Frameworks/App.framework/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			6eubeZZqyUNzwtBtT1r66GIIirzwERUZET0Gp6oe/CI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Flutter</key>
		<dict>
			<key>hash2</key>
			<data>
			hgArhF706OxYSMi1qwbPkEQ9uwBvQVLVJpu5EZnh+DM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			auaf7wPxiASCYD2ACy1dfbMJvmONwFvSz1BWYAQrrSw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			o0iigVsmgwmtZfSv3X7hReDNYP5rXblslDnqq2s6UQc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EXDk4t+7qCpyQkar+q9WHqY9bcK8eyohCwGVtBJhMy8=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0h9+vK5K+r8moTsiGBfs6+TM9Qog089afHAy3gbcwDU=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			kg195C3vZLiOn8KeFQUy7DoVuA9VZDpqoBLVn64uGaI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyqlHYuZbpFevVeny9Wdl0rVFgS7szIyssSiCyaaeFM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y5PEW7Tws4XpVmn15X/fdaU0d76hSeF803JlgrI20qE=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			RAOC6nDhZdghbAzsIZgVeq6qPt+MUNTfm/vkUnhmZO4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			I+878Ez9ZpVdAR2GOzKJKjaZ5m807AeAF++2pSMQss4=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nmZjZpvFCXrygf4U9aPkNi8VcI7cL5AtA+CY5uUWIL0=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ebBVHSZcUnAbN4hRcYq3ttt6++z1Ybc8KVSYhVToD5k=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4hl+kRU4PNNKdAHvYrliObXzSjRzow9Z18oOMRZIa0o=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HqbvCHqKWTzs5GjLAwupqEIYVi9yf5CrMdMe31EOwUA=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			+PMn+5SDj2Vd6RU8CQIt/JYl3T+8Dhp7HImqAzocoNk=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4kb25KahvBulQiElAYv/ZSbdkhkb5F/AKRZv4IjLsOw=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			phm49xhui2KkRomlqtmASHuU7lmd/s3qNCQpYxC0IMc=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WP7jjmDAou3izhqs6OFuBztGIVPrBws36TPv+YMgkTM=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			n5XX54YqS1a2btkmvW1iLSplRagn0ZhHJ4tDjVcdQhI=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			cyXVJgm8A9YmIH+dJMquQonPSAprqXYr6ObUJBeADlo=
			</data>
		</dict>
		<key>Frameworks/Flutter.framework/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			ZepykIMSjfzhwAcmupMrkaqvXkhza1ZE3TdHjl8odaw=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			oIx460RM4RKszgMdFv0ao73Ll7LytbkzPfnkrWuz34g=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			InxRF+lcRQAKVuBE3JRAUCK5It/zzzbyRpZr9XtBAK0=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation</key>
		<dict>
			<key>hash2</key>
			<data>
			xeQD7eVrMNjgIzFeEiypA6en4gxi4AGmA10t0qbE7LE=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Z7uFZLExSZPxw2TTgT1bYDV+riZM2wfSNIugehi/iAE=
			</data>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework/shared_preferences_foundation_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			BWQouTi9VwGKYAGdFcPB7i+nJ/I2h1mLu9k0hIsYCxo=
			</data>
		</dict>
		<key>Frameworks/sqflite.framework/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Zf7HOaKb3BqiK44y8FGNHpdPehzseI8aniJve/Wljd4=
			</data>
		</dict>
		<key>Frameworks/sqflite.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			eb/b6kOdr6wrBFwki5p7iwf3Au9lIszJRlweR2QG12o=
			</data>
		</dict>
		<key>Frameworks/sqflite.framework/sqflite</key>
		<dict>
			<key>hash2</key>
			<data>
			l3KpgFSPnF6KNYUE65klFYRprWcx8N2aUy80W3lQNVU=
			</data>
		</dict>
		<key>Frameworks/sqflite.framework/sqflite_darwin_privacy.bundle/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			yuOE7SdoJIKqjVPDcpir0wA13DbHcgFA2NdpOXm//Jc=
			</data>
		</dict>
		<key>Frameworks/sqflite.framework/sqflite_darwin_privacy.bundle/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			RyJqKWCN8gatChEOav61p3/1dawd+cdr/bLW37P6/tE=
			</data>
		</dict>
		<key>Runner.debug.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			JrdgudR+vV40xm6Xf3g5N+AkzCN/lOUTPqx6+YStTi0=
			</data>
		</dict>
		<key>__preview.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Cw6IvJp6fhsdQhbXSWx2nDOzdz+UFl9NM8Uui4GFyFM=
			</data>
		</dict>
		<key>embedded.mobileprovision</key>
		<dict>
			<key>hash2</key>
			<data>
			/JQRn6jX1T63ELTkOFWsd2f9DcAMCjAgs3LuEJl7zFU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
