-target arm64-apple-macos10.14 '-std=gnu11' -fobjc-arc -fmodules -gmodules '-fmodules-cache-path=/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/ModuleCache.noindex' '-fmodule-name=shared_preferences_foundation' -fpascal-strings -O0 -fno-common '-DPOD_CONFIGURATION_DEBUG=1' '-DDEBUG=1' '-DCOCOAPODS=1' '-DOBJC_OLD_DISPATCH_PROTOTYPES=0' -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.2.sdk -g -iquote /Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/shared_preferences_foundation-generated-files.hmap -I/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/shared_preferences_foundation-own-target-headers.hmap -I/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/shared_preferences_foundation-all-non-framework-target-headers.hmap -ivfsoverlay /Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-8699adb1dd336b26511df848a716bd42-VFS/all-product-headers.yaml -iquote /Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/shared_preferences_foundation-project-headers.hmap -I/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/include -I/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/DerivedSources-normal/arm64 -I/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/DerivedSources/arm64 -I/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/DerivedSources -F/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation -F/Users/<USER>/Documents/Flutter/flutter/bin/cache/artifacts/engine/darwin-x64