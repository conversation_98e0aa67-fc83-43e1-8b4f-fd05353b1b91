{
  'version': 0,
  'use-external-names': 'false',
  'case-sensitive': 'false',
  'roots': [{
    'type': 'directory',
    'name': "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Modules"
    'contents': [{
      'type': 'file',
      'name': "module.modulemap",
      'external-contents': "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/unextended-module.modulemap",
    }]
    },
    {
    'type': 'directory',
    'name': "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers"
    'contents': [{
      'type': 'file',
      'name': "shared_preferences_foundation-Swift.h",
      'external-contents': "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/unextended-interface-header.h",
    }]
    }]
}
