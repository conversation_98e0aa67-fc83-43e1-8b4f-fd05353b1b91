{"case-sensitive": "false", "roots": [{"contents": [{"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Pods/Target Support Files/Pods-Runner/Pods-Runner-umbrella.h", "name": "Pods-Runner-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/Pods_Runner.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/Pods-Runner.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/Pods_Runner.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Pods/Target Support Files/shared_preferences_foundation/shared_preferences_foundation-umbrella.h", "name": "shared_preferences_foundation-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/shared_preferences_foundation.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Modules", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Headers/shared_preferences_foundation-Swift.h", "name": "shared_preferences_foundation-Swift.h", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/shared_preferences_foundation/shared_preferences_foundation.framework/Versions/A/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteCursor.h", "name": "SqfliteCursor.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDB.h", "name": "SqfliteDarwinDB.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabase.h", "name": "SqfliteDarwinDatabase.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseAdditions.h", "name": "SqfliteDarwinDatabaseAdditions.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinDatabaseQueue.h", "name": "SqfliteDarwinDatabaseQueue.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinImport.h", "name": "SqfliteDarwinImport.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDarwinResultSet.h", "name": "SqfliteDarwinResultSet.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteDatabase.h", "name": "SqfliteDatabase.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteImport.h", "name": "SqfliteImport.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqfliteOperation.h", "name": "SqfliteOperation.h", "type": "file"}, {"external-contents": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/darwin/Classes/SqflitePlugin.h", "name": "SqflitePlugin.h", "type": "file"}, {"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Pods/Target Support Files/sqflite/sqflite-umbrella.h", "name": "sqflite-umbrella.h", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/sqflite/sqflite.framework/Headers", "type": "directory"}, {"contents": [{"external-contents": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Pods.build/Debug/sqflite.build/module.modulemap", "name": "module.modulemap", "type": "file"}], "name": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products/Debug/sqflite/sqflite.framework/Modules", "type": "directory"}], "version": 0}