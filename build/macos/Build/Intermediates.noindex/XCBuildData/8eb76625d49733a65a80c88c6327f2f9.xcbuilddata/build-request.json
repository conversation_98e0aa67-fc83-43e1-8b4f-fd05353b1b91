{"buildCommand": {"command": "build", "skipDependencies": false, "style": "buildOnly"}, "configuredTargets": [{"guid": "18c1723432283e0cc55f10a6dcfd9e02a26d6e580d3222b86d62fb80ec380c14"}], "containerPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Runner.xcworkspace", "continueBuildingAfterErrors": false, "dependencyScope": "workspace", "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "macosx", "sdk": "macosx15.2", "sdkVariant": "macos", "supportedArchitectures": ["arm64e", "arm64", "x86_64"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex", "buildProductsPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products", "derivedDataPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos", "indexDataStoreFolderPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Index.noindex/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Index.noindex/PrecompiledHeaders", "pchPath": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/PrecompiledHeaders"}, "configurationName": "Debug", "overrides": {"commandLine": {"table": {"COMPILER_INDEX_STORE_ENABLE": "NO", "OBJROOT": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex", "SYMROOT": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Products"}}, "synthesized": {"table": {"ACTION": "build", "ENABLE_PREVIEWS": "NO", "ENABLE_XOJIT_PREVIEWS": "YES"}}}}, "schemeCommand": "launch", "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}