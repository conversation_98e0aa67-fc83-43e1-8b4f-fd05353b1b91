{"": {"diagnostics": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/Runner-master.dia", "emit-module-dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/Runner-master-emit-module.d", "emit-module-diagnostics": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/Runner-master-emit-module.dia", "swift-dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/Runner-master.swiftdeps"}, "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/DerivedSources/GeneratedAssetSymbols.swift": {"const-values": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.d", "diagnostics": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.dia", "index-unit-output-path": "/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "llvm-bc": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.bc", "object": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.o", "swift-dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedAssetSymbols~partial.swiftmodule"}, "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Flutter/GeneratedPluginRegistrant.swift": {"const-values": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.d", "diagnostics": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.dia", "index-unit-output-path": "/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o", "llvm-bc": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.bc", "object": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o", "swift-dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant~partial.swiftmodule"}, "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Runner/AppDelegate.swift": {"const-values": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.d", "diagnostics": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.dia", "index-unit-output-path": "/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.o", "llvm-bc": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.bc", "object": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.o", "swift-dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/AppDelegate~partial.swiftmodule"}, "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/macos/Runner/MainFlutterWindow.swift": {"const-values": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.swiftconstvalues", "dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.d", "diagnostics": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.dia", "index-unit-output-path": "/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.o", "llvm-bc": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.bc", "object": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.o", "swift-dependencies": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow.swiftdeps", "swiftmodule": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/macos/Build/Intermediates.noindex/Runner.build/Debug/Runner.build/Objects-normal/arm64/MainFlutterWindow~partial.swiftmodule"}}