<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		yxv1a2BjaTIAD1Ye63vf3R+QhUU=
		</data>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<data>
		2dl3uKr7RLtKN9HDglJr1yqzUnk=
		</data>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<data>
		STxztQf8PlryljoA8JwV4Qmq/+w=
		</data>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<data>
		vKJkVIcw+LGHFnKJGwrQwCREv68=
		</data>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<data>
		8Zk3kTac4PWglLbe6YUB5E5gPxU=
		</data>
		<key>Resources/flutter_assets/assets/icons/icon_download.png</key>
		<data>
		ReZMJEmf4XYCwCk8j3qoThmYAwo=
		</data>
		<key>Resources/flutter_assets/assets/icons/icon_like.png</key>
		<data>
		zSWky9Bml4ejOh0aWiIAhhs8qnY=
		</data>
		<key>Resources/flutter_assets/assets/icons/icon_more.png</key>
		<data>
		w6PWgtQ4VoWRXknzty+fk1JQmoY=
		</data>
		<key>Resources/flutter_assets/assets/icons/icon_nav_back.png</key>
		<data>
		h03IvWujya00IWzXdAvn0Hwsdcw=
		</data>
		<key>Resources/flutter_assets/assets/icons/icon_unlike.png</key>
		<data>
		soMZHpfaX3rQbGKBQDjOEpKYfFA=
		</data>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>Resources/flutter_assets/isolate_snapshot_data</key>
		<data>
		SIm4/1Paq4sWKgTicGS1/n3+2I0=
		</data>
		<key>Resources/flutter_assets/kernel_blob.bin</key>
		<data>
		omqOflD9IzvikVU+RPkquEDKsBs=
		</data>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		ZOn9DadrDaxGtj5fyHAxrQx9qqs=
		</data>
		<key>Resources/flutter_assets/vm_snapshot_data</key>
		<data>
		ap40xllbguuMfkV4mJkKh/NQWg4=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			wCWlrRVPbO84WsrdcQvLMq6Yz/zmZ2OX8Vz6nMChxzY=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			57dpbHWrloluavLMDwmTTNlgawAfzmj+Yl3FIV400/U=
			</data>
		</dict>
		<key>Resources/flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8pPe9oz5w8eDYuh8t5U8Z4lrpb7pNS77/3F3c/Gsn5g=
			</data>
		</dict>
		<key>Resources/flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zX4DZFvESy3Ue3y2JvUcTsv1Whl6t3JBYotHrBZfviE=
			</data>
		</dict>
		<key>Resources/flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			za4tyYBhJ0juxiVhcD5Lsp9dopHa7a7YFkeqJEVP8Qw=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/icons/icon_download.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GQe6GNTB1MG60X7lD+/RX1NMqa1f85R3Wes178RR1II=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/icons/icon_like.png</key>
		<dict>
			<key>hash2</key>
			<data>
			m8QUjoorlK+JQGuPLXCueLNAnnQkNMc4uH0VTh7aK4o=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/icons/icon_more.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qjVeMLNC6hq/0A9pv8fz3fk6OWw/gaesIfC1Y2pMM+Q=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/icons/icon_nav_back.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RecIGmPPFCRLEvQkxm9/9S+NZGXzpbaO/adRGTl7TSY=
			</data>
		</dict>
		<key>Resources/flutter_assets/assets/icons/icon_unlike.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7AtoYmIr0fm/DrWMlv/bAgejAqEFqOsPQ4UeVjKQQI8=
			</data>
		</dict>
		<key>Resources/flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>Resources/flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			anOb2f9bedJ1njfLAJCJBLpvzqbr//Igm8Bh1zQ9Yk4=
			</data>
		</dict>
		<key>Resources/flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			O0n3BZR6F1PK5Jtgdihg5RZcnJhGG7NkdF21+7JoP2Y=
			</data>
		</dict>
		<key>Resources/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>Resources/flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			DvnZAS4Xn/vrjon0XahwZsfayB9xW3f3ALepxvZLFGY=
			</data>
		</dict>
		<key>Resources/flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			6eubeZZqyUNzwtBtT1r66GIIirzwERUZET0Gp6oe/CI=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
