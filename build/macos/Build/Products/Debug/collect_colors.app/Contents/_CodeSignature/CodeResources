<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/AppIcon.icns</key>
		<data>
		aAxGg0Q+dQoQhfAk9AHVCAev338=
		</data>
		<key>Resources/Assets.car</key>
		<data>
		+hMVT7fL9g7bWsIG7FMtIeUuOHc=
		</data>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<data>
		9tDgKIyMssBlJmfHUKQc4o3ECzk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Frameworks/App.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			XfpjUTE9UKgVBLgkJhoESUeHHnM=
			</data>
			<key>requirement</key>
			<string>cdhash H"5dfa6351313d50a81504b824261a044947871e73"</string>
		</dict>
		<key>Frameworks/FlutterMacOS.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			JxA9V2WkO929xe22Td9m70ciqwo=
			</data>
			<key>requirement</key>
			<string>cdhash H"27103d5765a43bddbdc5edb64ddf66ef4722ab0a"</string>
		</dict>
		<key>Frameworks/libswiftAppKit.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			/OmwGRbORcnNjnoLmSHJ5YPdYTA=
			</data>
			<key>requirement</key>
			<string>cdhash H"93e4a579dc797d359af753c1d9875e1e368a7be4" or cdhash H"fce9b01916ce45c9cd8e7a0b9921c9e583dd6130"</string>
		</dict>
		<key>Frameworks/libswiftCore.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			GX4mmXq+7/mQoj8oZdn8Hutw0Y8=
			</data>
			<key>requirement</key>
			<string>cdhash H"5896a3c5c845fa8e206333a333969a967f159eeb" or cdhash H"197e26997abeeff990a23f2865d9fc1eeb70d18f"</string>
		</dict>
		<key>Frameworks/libswiftCoreAudio.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			A7UrDoeeg9NbAL6zhhfNao2U+rc=
			</data>
			<key>requirement</key>
			<string>cdhash H"addbf0ec5c37ef636ad1edbe3596d1ecf1a9db42" or cdhash H"03b52b0e879e83d35b00beb38617cd6a8d94fab7"</string>
		</dict>
		<key>Frameworks/libswiftCoreData.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			RgOKaMtfkc0qUMlFJgiXpeQ4Sdo=
			</data>
			<key>requirement</key>
			<string>cdhash H"9ffc88ce5cb9c9b9acd49f00caa1b083efdbe122" or cdhash H"46038a68cb5f91cd2a50c945260897a5e43849da"</string>
		</dict>
		<key>Frameworks/libswiftCoreFoundation.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			BH44Vy47Lx+AwIteCd/JCOP+GvY=
			</data>
			<key>requirement</key>
			<string>cdhash H"8fa7f3c353eb61af2b7fc8c12269195f74bea074" or cdhash H"047e38572e3b2f1f80c08b5e09dfc908e3fe1af6"</string>
		</dict>
		<key>Frameworks/libswiftCoreGraphics.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			k0cSEkTzmj4aFYrjlQ01bP4lHFc=
			</data>
			<key>requirement</key>
			<string>cdhash H"3604106a6a3cb32759dbdec9dd34797d3641fe01" or cdhash H"9347121244f39a3e1a158ae3950d356cfe251c57"</string>
		</dict>
		<key>Frameworks/libswiftCoreImage.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			71/02RN7QBBmkWLMRa2YS0OEzOs=
			</data>
			<key>requirement</key>
			<string>cdhash H"1c94028a679fd3abc70acbf9a125258c112c0371" or cdhash H"ef5ff4d9137b4010669162cc45ad984b4384cceb"</string>
		</dict>
		<key>Frameworks/libswiftCoreMedia.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			wKq5+CadziNvyHnxt5nSaAMhEdk=
			</data>
			<key>requirement</key>
			<string>cdhash H"fcfdbfac49b22f22aff1a259db747bf573307d2a" or cdhash H"c0aab9f8269dce236fc879f1b799d268032111d9"</string>
		</dict>
		<key>Frameworks/libswiftDarwin.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			J8aEbcH96rf3olM1v8wJQms7xaI=
			</data>
			<key>requirement</key>
			<string>cdhash H"300b03492cc2be4543d5f2be1e06909f387af72d" or cdhash H"27c6846dc1fdeab7f7a25335bfcc09426b3bc5a2"</string>
		</dict>
		<key>Frameworks/libswiftDispatch.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			1DtZZpeByuwlmdUB3e/N6JGfZ14=
			</data>
			<key>requirement</key>
			<string>cdhash H"bab33e638bc83f0268af02eebce6bf5dd6b3c51f" or cdhash H"d43b59669781caec2599d501ddefcde8919f675e"</string>
		</dict>
		<key>Frameworks/libswiftFoundation.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			8t0+NaUGfny0OE73x/qjMHlQjFE=
			</data>
			<key>requirement</key>
			<string>cdhash H"d7e9fb967f6385f8a8b790ae5a59c01d83b2672c" or cdhash H"f2dd3e35a5067e7cb4384ef7c7faa33079508c51"</string>
		</dict>
		<key>Frameworks/libswiftIOKit.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			pQPvcS1eE2AmKSN5CMtcZiMT4PQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"f5bd61791fbc60c97936524b070348de57792878" or cdhash H"a503ef712d5e13602629237908cb5c662313e0f4"</string>
		</dict>
		<key>Frameworks/libswiftMetal.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			tKr6i2JJtHEFLkK/fMudC093jZ4=
			</data>
			<key>requirement</key>
			<string>cdhash H"1291840c54eedc74a0c7b25a0aa8a3ea8ba2dd1f" or cdhash H"b4aafa8b6249b471052e42bf7ccb9d0b4f778d9e"</string>
		</dict>
		<key>Frameworks/libswiftObjectiveC.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			OwCGe/c9CItCxNr6jAps2hesJYA=
			</data>
			<key>requirement</key>
			<string>cdhash H"5f949b391729fb4cb0aac45efc729edf52fc94ea" or cdhash H"3b00867bf73d088b42c4dafa8c0a6cda17ac2580"</string>
		</dict>
		<key>Frameworks/libswiftQuartzCore.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			AtxA7yzVGYRZPoPfhZZSpfLEEyU=
			</data>
			<key>requirement</key>
			<string>cdhash H"e02d111c168bcdb2d35b89d2b165838e343b452e" or cdhash H"02dc40ef2cd51984593e83df859652a5f2c41325"</string>
		</dict>
		<key>Frameworks/libswiftXPC.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			uFKhujvCOYyZ4lm3G/9ojAmusY8=
			</data>
			<key>requirement</key>
			<string>cdhash H"fcc4f33e2b4fdf2be11ccb50f72a1365b70294a8" or cdhash H"b852a1ba3bc2398c99e259b71bff688c09aeb18f"</string>
		</dict>
		<key>Frameworks/libswiftos.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			c+BBGYIvLYzV7rQm3awrK5gpXe8=
			</data>
			<key>requirement</key>
			<string>cdhash H"3406aab10f486b91c2d9334de341504de56a5631" or cdhash H"73e04119822f2d8cd5eeb426ddac2b2b98295def"</string>
		</dict>
		<key>Frameworks/libswiftsimd.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			yoevzZNzSjWnQqIYLmXdd/DhGvQ=
			</data>
			<key>requirement</key>
			<string>cdhash H"a91aa60521c71dd382e53441689bc0970469be1b" or cdhash H"ca87afcd93734a35a742a2182e65dd77f0e11af4"</string>
		</dict>
		<key>Frameworks/shared_preferences_foundation.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			nMmMryhes7+Ha74F0SMT2X6pr3A=
			</data>
			<key>requirement</key>
			<string>cdhash H"9cc98caf285eb3bf876bbe05d12313d97ea9af70"</string>
		</dict>
		<key>Frameworks/sqflite.framework</key>
		<dict>
			<key>cdhash</key>
			<data>
			1ot3wXQZ8DjoEg5mdy7QEMbA1pk=
			</data>
			<key>requirement</key>
			<string>cdhash H"d68b77c17419f038e8120e66772ed010c6c0d699"</string>
		</dict>
		<key>MacOS/__preview.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			whTadanJe8FUNpjcOS9irQlc2GU=
			</data>
			<key>requirement</key>
			<string>cdhash H"c214da75a9c97bc1543698dc392f62ad095cd865"</string>
		</dict>
		<key>MacOS/collect_colors.debug.dylib</key>
		<dict>
			<key>cdhash</key>
			<data>
			f2flipXCemAjxJbVH17AFU9UZMk=
			</data>
			<key>requirement</key>
			<string>cdhash H"7f67e58a95c27a6023c496d51f5ec0154f5464c9"</string>
		</dict>
		<key>Resources/AppIcon.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			cmkXXoTx+ZUjFC50oDL4Cmb6f++oBA9wPejlIra72Gc=
			</data>
		</dict>
		<key>Resources/Assets.car</key>
		<dict>
			<key>hash2</key>
			<data>
			zWrnysXUEaxrEjWvwoGx+Gl5C5cYgKJKaQ4ne7aBOKo=
			</data>
		</dict>
		<key>Resources/Base.lproj/MainMenu.nib</key>
		<dict>
			<key>hash2</key>
			<data>
			BC2M2WmYAGNSSbm9h0UVXjeCTU3a4MRto1Gpgsmy3jM=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
