{"inputs": ["/Users/<USER>/Documents/Flutter/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Flutter/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/flutter_build/dad7ba8c6d34d1cd7d9334760a838f26/app.dill", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/flutter_build/dad7ba8c6d34d1cd7d9334760a838f26/App.framework/App", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/pubspec.yaml", "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "/Users/<USER>/Documents/Flutter/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Flutter/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/shader_compiler.dart", "/Users/<USER>/Documents/Flutter/flutter/bin/internal/engine.version", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/pubspec.yaml", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/ios/Runner/Info.plist", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/ios/Flutter/AppFrameworkInfo.plist", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_more.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_download.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_nav_back.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_like.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_unlike.png", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/Flutter/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.6.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-2.0.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.11.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.2.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common_ffi-2.3.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.4.3/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.6.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-13.0.0/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/LICENSE", "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "/Users/<USER>/Documents/Flutter/flutter/bin/cache/pkg/sky_engine/LICENSE", "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/LICENSE", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/DOES_NOT_EXIST_RERUN_FOR_WILDCARD449962790"], "outputs": ["/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/App", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/Info.plist", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_more.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_download.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_nav_back.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_like.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_unlike.png", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/NOTICES.Z"]}