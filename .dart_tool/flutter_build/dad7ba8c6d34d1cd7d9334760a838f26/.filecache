{"version": 2, "files": [{"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/scaffold.dart", "hash": "de97658f215739c29a5aa9e92624224a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/native_assets.dart", "hash": "afb2489e20087a1dc302a8ebff136e24"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/flexible_space_bar.dart", "hash": "36b531dcc56b54629a34fa54d7ff0a37"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_aware_image_provider.dart", "hash": "5d34c419faa453f50535c81a93de00d0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_fuchsia.dart", "hash": "9611dde0a187002284ea247706faa999"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/web-0.5.1/LICENSE", "hash": "d53c45c14285d5ae1612c4146c90050b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/star_border.dart", "hash": "8f2f569174686e111a70557c7a0257a3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/expand_icon.dart", "hash": "9b22018b56b151182e96ffdd74be318c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/unmodifiable_wrappers.dart", "hash": "4be4077b482b12a5ee202d859e8286df"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/flutter_build/dart_plugin_registrant.dart", "hash": "0f36404b4f37b4050ce60660661a2a12"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/autocomplete.dart", "hash": "cdc746e4832e56cc9ab4f97d6c31df09"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/flutter_logo.dart", "hash": "32187ab06a29c3f5929b9f26fd5ccb8b"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_download.png", "hash": "91cc8e32a6e62622efce20b40f6825e5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/oval_border.dart", "hash": "2eb2de173e53bab6a0a033f89856eae6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/text_scaler.dart", "hash": "ee7a8f724628dece0f9529c8b95a973e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/selectable_region.dart", "hash": "aee8484d7e33eac78d4bd7de89df100d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/allocation.dart", "hash": "7c8e196c6c96efaadb0e605ec3cb1cce"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/icon_theme_data.dart", "hash": "eca4f0ff81b2d3a801b6c61d80bc211c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/sql_builder.dart", "hash": "594051e470cc32125f29b8aa33cc4206"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/platform_view.dart", "hash": "799a4ef2a4bf9f5a72c65bac4ecf23a4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/bottom_navigation_bar.dart", "hash": "4e0be4ceeeb7798049278b769bc06184"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/page_storage.dart", "hash": "20ff58eb86f7132e7b2a18f0442305e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_expressive.dart", "hash": "3826eca923efb51fd68c08bd75a0226f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/icon_tree_shaker.dart", "hash": "e299e009504c2f2e5ad0c77ca151ddc8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/table.dart", "hash": "29e1858c5ebc2b4dc6d1528196bfb1b6"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_more.png", "hash": "ce00be35e2da59233b555ca38d60295f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/switch_theme.dart", "hash": "444589d7a3a418a8388003283b096007"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/colors.dart", "hash": "cadfa2c03911a1d63eab8e1d12f78fcc"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/main.dart", "hash": "bff6fe12dff98f6e87f4624fa3ba052d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector.dart", "hash": "6a67d38bafe568f1b4047286d586fbbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/platform/platform_io.dart", "hash": "bb7e4bee2f9cca7b7e771e5ff413108d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_android.dart", "hash": "782acd65a3be12036e75f58a3f63ae14"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/lookup_boundary.dart", "hash": "0a3c66e5de5f99b50a256aac5e4207e6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/tween_sequence.dart", "hash": "ceca8c46e07b211bd755e480b1bd6b32"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/ui/sampling/sampling_color_widget.dart", "hash": "abd3f837b74901589960ce0c81d53aaf"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/transitions.dart", "hash": "e09e9bdea673385316dff42aa4f0d011"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_web.dart", "hash": "b647083393854c49d70b3c1283650b5f"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/ui/color/collect_color_widget.dart", "hash": "add938a74f4c68cc14549c9085e24143"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/button_bar.dart", "hash": "fe9c2aa616e22ceb52299e47223f5de6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/app_bar_theme.dart", "hash": "2f92c28411483032fb7c0a851ebbbb5a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/actions.dart", "hash": "314ecdcd204b96206b2fee781e290151"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/src/point_provider.dart", "hash": "7504c44d1fa6150901dd65ec78877be0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/database.dart", "hash": "58b3ba88bbcf26534262ce680263e08d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/factory_impl.dart", "hash": "65614758273c0b82b4ce22b3728be36c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb3.dart", "hash": "257ca4608e7d75f1db8d4c3ab710ac70"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/keyboard_key.g.dart", "hash": "c005befed4f9bbe647edba9b0b9c7a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.0/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/adaptive_text_selection_toolbar.dart", "hash": "ac7c22640ca7966067609730ca0d9b5a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/curves.dart", "hash": "30c1cf82a3de387f7f4b840168cc473a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/time_picker.dart", "hash": "459339769d2437724c94d115488cff54"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/flow.dart", "hash": "79ac8ad87caa659775def3b2860e5a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/dynamic_scheme.dart", "hash": "ce4df222c034641e9cc494b4e8724f43"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_neutral.dart", "hash": "11c6c659a99b5530d764fa41f4bc81f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/common.dart", "hash": "493b51476fc266d10a636f520fff01fc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/spell_check.dart", "hash": "e108a994c8804321ed7d12f9dde58033"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/context_menu_action.dart", "hash": "4e84dc6a842f68f7ff0f2bfc6e728975"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/constants.dart", "hash": "0cb06ef1fbbec09f85b6b40cdeaa2f9a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/lib/shared_preferences_foundation.dart", "hash": "b72ebe27944e3a75601e56579bb92907"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/multitap.dart", "hash": "8872d7a29893023889c704eb502c0fa3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/play_pause.g.dart", "hash": "cee61ff4bc1494858ec39f8c4f09c1a6"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/NOTICES.Z", "hash": "9ac138fee6c4b47c1cacd41c88ece19a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/src/point_provider_lab.dart", "hash": "6566a35ff0dea9376debf257bdb08fba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/circle_border.dart", "hash": "3cc57e362f339f40562153a37b8e7ef2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/scrollbar_theme.dart", "hash": "999271058c643ec9c2e44eeaf593fbe2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/primary_scroll_controller.dart", "hash": "1c43aa902b27d1a8936c77dcf231953b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/_capabilities_io.dart", "hash": "25e9902b35a6a19cbc5ad98f2c1232ba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/colors.dart", "hash": "c00a03b3a6a8307cb3d7f2b482c525c1"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_download.png", "hash": "91cc8e32a6e62622efce20b40f6825e5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/system_chrome.dart", "hash": "104d3fdf6ff2b0e8a45a2222b148e8ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common_ffi-2.3.3/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterator.dart", "hash": "6c54f90e0db5f42a13be6b3efeb4a04d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_position_with_single_context.dart", "hash": "fb2ea411a2459b73756c5b9b138d8bb8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/theme.dart", "hash": "9ad2220e74ba95b18a8c9e48acbb8ddb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/shader_warm_up.dart", "hash": "ff83f5dd99744d061d9eaf3c37082699"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/slider.dart", "hash": "0bd3a2442a2b2fb190b822803f77c657"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/lib/src/shared_preferences_async_foundation.dart", "hash": "282aeeb78f4a92064354b5fe98161484"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/input_date_picker_form_field.dart", "hash": "c26b63312c7f74998f5a1e2a9a14f187"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_anchors.dart", "hash": "7232c49ac98baf073c51b07172549629"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/debug.dart", "hash": "fbaf91ff4796f047f0b69118d81502ba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/progress_indicator_theme.dart", "hash": "8effe6176ace6ada9ad1db0370cf2e78"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/ink_well.dart", "hash": "ebef46cd0d422829fe68f337da8707a0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/utils.dart", "hash": "8a7e3b181572ed50e923e5dc05a7533d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-13.0.0/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/arc.dart", "hash": "fe52344c0c5470d7f044c347de68987e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/hybrid_printer.dart", "hash": "c7ea8e1b642822fe4d241be13ab160fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/LICENSE", "hash": "8f29b74ba6fa81721ca1cd98cd39ae4d"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/vm_snapshot_data", "hash": "ba4bfa233d4fb174527c1c7a78964e42"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/default_text_editing_shortcuts.dart", "hash": "3b66360268cfff987129801d27b3f725"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar_button.dart", "hash": "af3cac4b25350f32615ddef14a0beb6c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/platform_views.dart", "hash": "e51eedb52c193fc8979d8fa6b2391f96"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/navigation_bar.dart", "hash": "3b684352933cdd3d75d9d932a7382dd7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/feedback.dart", "hash": "049d34ba2849061a547be27fc6e122fb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/localizations.dart", "hash": "4f4fcae47233bec91490b2c694f410d3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/activity_indicator.dart", "hash": "d1fe16c93c45c3958060d1c4f4134163"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tooltip.dart", "hash": "6bdca714dc728711bd0f44bfb5f06b9d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/adapter.dart", "hash": "0192533b9be6f394b49a75b38f8dc84d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/magnifier.dart", "hash": "69b800f2638c55fdf856dca63b9a95c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/types.dart", "hash": "13e6a7389032c839146b93656e2dd7a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/database_mixin.dart", "hash": "44e15221f9d00bfd8fe3b4ed644379c9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/directory.dart", "hash": "8f4de032f1e2670ca51ce330a4de91a3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/two_dimensional_viewport.dart", "hash": "9b03091710d3208117ed01cc6bbfd895"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/lsq_solver.dart", "hash": "234f5667a312bcca30a59e788fe46424"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-3.0.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/animated_scroll_view.dart", "hash": "6f8e3ff3a7622d14880b5520f9940a19"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_fidelity.dart", "hash": "06b65be6c068a8e2fb674093d4b3d5af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector4.dart", "hash": "299bd3979d7999412945ac4e3199cdcf"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/scheduler/ticker.dart", "hash": "4a8bd9b837a2cd93aa03cfe1fec162b2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/text_editing_intents.dart", "hash": "7776c5eaa171bb5e03d1945d85354f49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_monochrome.dart", "hash": "3d4578209c85304350580f859fbcbc49"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/slotted_render_object_widget.dart", "hash": "fd48427e65c5910cbba1fc3e4e57cfcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/compat.dart", "hash": "75e9e8da5881b6c2ebedc871d7bbc064"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/list_wheel_scroll_view.dart", "hash": "80f6aedbf2a6a53aac40d69b7563d550"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_selection_toolbar.dart", "hash": "e2a64a3c80446ead09aa0f6254488cb6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/process_text.dart", "hash": "7eac1f53ea4f37ec117007b9280b2240"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_directory.dart", "hash": "62da8696885bd25977675ac4f7f1aef9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/toggleable.dart", "hash": "78c959441f4640a312296863322d5767"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_persistent_header.dart", "hash": "906d10bd455e570308711737b1511c01"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/search_view_theme.dart", "hash": "f7db08701d88d1347f7275df15a9ab8b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/checkbox_list_tile.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/cupertino.dart", "hash": "1d4c33ba865998a6c9ecd3e74820a31b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/mouse_tracking.dart", "hash": "85defece750eb4f5a926e6d16c23a319"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_list.dart", "hash": "5b894ae18be3e2442a34288833184ca9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.11.0/lib/meta_meta.dart", "hash": "d2de90fa1fc5398b7fdd4bdc3bbb2178"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/utils/utils.dart", "hash": "0761976d50f2e73e9cd0fe8763e3678a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/binding.dart", "hash": "93d90491bdbf6a3c7a59db448a631c61"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/dismissible.dart", "hash": "558d215f7105ac625d40769834346f76"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/hct/viewing_conditions.dart", "hash": "cb0d5b80330326e301ab4d49952b2f34"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/App", "hash": "8548b7470904c7328306b1f74616ac9a"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/ui/color/detail/color_detail_widget.dart", "hash": "44661fc7424fdca85f1d654f824c62a6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/persistent_hash_map.dart", "hash": "32138f3bdec292a8c32139eaf23d6bcc"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/db.dart", "hash": "460dedfe727aeb45eb7bdf2774e83e4a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/debug.dart", "hash": "4f524e2721f605ade9b1a11acc166534"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/magnifier.dart", "hash": "03d33606872f9e7e6745ed7b034cae70"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/outlined_button_theme.dart", "hash": "2b9a24c4f3c66c9847e794ddbd1e7249"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/expansion_panel.dart", "hash": "a04cc18bc7eefbe1e8c918a8a00cac5a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar_button.dart", "hash": "1dc7dcdd70674a9f80245280f277e7ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.2/lib/src/messages.g.dart", "hash": "b7da87cd8a3a59719e89e64495d44a76"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/licenses.dart", "hash": "41b759e0b973e7c0b9be392c1af005c9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/gradient.dart", "hash": "d756414ecf29647410f904fdb6dad2de"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/tab_view.dart", "hash": "787b07885c28afd76b3eb5ccf43996e5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/toggle_buttons_theme.dart", "hash": "eca62c60db96d71f3cae9b506875c03a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/outlined_button.dart", "hash": "2d32a51682b0b8c5b19ba67c181b20c8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/popup_menu_theme.dart", "hash": "dd72e2136afe8f5933dcd2ac028fd69d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/router.dart", "hash": "19782f3406c04eb88023156185cca15c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/ellipsis_search.g.dart", "hash": "c9c0ff593fcabc29c3234b4e1bf2ac38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.1/LICENSE", "hash": "901fb8012bd0bea60fea67092c26b918"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/canonicalized_map.dart", "hash": "889042dc1cc5b1f4e4e1572270920f54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/sqflite_impl.dart", "hash": "8e1d2c37f506b65c7d8b3274456d8dfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.2/lib/shared_preferences_android.dart", "hash": "667f26b65b92754564c58509cfb15a78"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/sliver.dart", "hash": "c2df040ed997fead899bba9918a91cc4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/dialog.dart", "hash": "cc1d5dfd73710d41b5ffa99b498efd5d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/desktop_text_selection_toolbar.dart", "hash": "4c75638ad31731ec9908b311ea075a5c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/navigator_pop_handler.dart", "hash": "b24332ea7edd9debca07dc0a0ee17f23"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/decorated_sliver.dart", "hash": "52bd396bd9c86097e53528c764af4a2d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/filters/development_filter.dart", "hash": "a925c024faf2d8bc047793e5a39b95d7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/_network_image_io.dart", "hash": "682907a0e9e60ab53b752dde1e45381a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/utils.dart", "hash": "9657eaf8d49132bb6343a91c0a3f9dba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/mergeable_material.dart", "hash": "0f018883b707c020f75a36fe2494eb6a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/autofill.dart", "hash": "2c3db13235dd0c924d1367692ec4ae1f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/will_pop_scope.dart", "hash": "347ca56667b68d9d66174f8200b4505e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/ink_splash.dart", "hash": "c2061e56218adab22cbf2df63c7130c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/value_utils.dart", "hash": "0d3f006ee8b1e0b598224d155877144b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/service_extensions.dart", "hash": "b5d6c349fa0259f1516951989e4d5bbc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.2.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/matrix_utils.dart", "hash": "8864321130eabbc4f8e2fe9bf68c452b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/sqlite_api.dart", "hash": "639fb32a06c4cdc655546306790da302"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/shape_decoration.dart", "hash": "f895208979505fabfd81ee2b74940eae"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/ticker_provider.dart", "hash": "8991dbee9ddb91b4a98f28f51e6d69d6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface.dart", "hash": "5145b27b3db429f9f1da26cfe563bd02"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/toggleable.dart", "hash": "cd4a150ca2f954c9bb946bc5e0db4510"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/service_extensions.dart", "hash": "ffef3a071fe16574982a927c9f273f7a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/constants.dart", "hash": "a22042c948166ba677133268fafc4b41"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/windows.dart", "hash": "0d86d4ba2e01e5e62f80fcf3e872f561"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_map.dart", "hash": "700328ab0177ddfd9a003a8c15619c1a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/interface_level.dart", "hash": "052024fa5156096baa193bacd244f789"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/constant.dart", "hash": "8d5660686b2687f3947b822758c82942"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/blend/blend.dart", "hash": "f487ad099842793e5deeebcc3a8048cb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/route.dart", "hash": "c5b2ea70ffa4b1b1845753d58f76dd2a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/input_chip.dart", "hash": "a0f5f092f8b948c8aca71ecadbab716d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/page.dart", "hash": "f50ee6c376dbbd2fb413743b2ff41f86"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/lib/src/messages.g.dart", "hash": "1567572a579e5f2aab31966d4a056855"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_theme.dart", "hash": "0a7fb012dbb1e0884d7ca11638b2d5c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/path_provider_linux.dart", "hash": "8ac537f4af05ad812e8cd29f077aee24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/mixin/platform.dart", "hash": "b92ed901e8df2fde6d4739ed5e59051d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/collection.dart", "hash": "476383869aff7b87579a7753e47722d7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/change_notifier.dart", "hash": "991446f6d6825817d9446baee83b6caf"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_exception.dart", "hash": "b062a8e2dade00779072d1c37846d161"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/print.dart", "hash": "e709d79a1d6ee7e1e772c834ce24e527"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/sliver_fill.dart", "hash": "8a7d5e7f76b8574a2c42c596183dcee4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/dynamiccolor/dynamic_color.dart", "hash": "5a135e72a0650a7de37b7d9195b0e47c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/stadium_border.dart", "hash": "bcc538ad064b96769f9b19ccc421038f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_vibrant.dart", "hash": "05c0986996f5c9a57cad9b0d2d370ad4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/size_changed_layout_notifier.dart", "hash": "5528b93def00b5b750c964a10f323900"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/semantics/binding.dart", "hash": "f5cf32b8d6bae401ce2a48f05724500d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/paragraph.dart", "hash": "297e7510766cd8855a1e8b4ed3fb4f2c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/hct/hct.dart", "hash": "596fb2e55b1ff1662e4bd67461fdc89d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/back_button.dart", "hash": "035b8d3642fa73c21eafbee7851cc85d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons.dart", "hash": "26e8edddc50361d04ffdac680bcfeeca"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/animated_cross_fade.dart", "hash": "27a4b56c4d01951af2fa11493565ae9d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/compat.dart", "hash": "8a31d0709de6865d3f49374ab6bb274a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/range_slider.dart", "hash": "abb9343cbdc85694676eb7ca5ae9da60"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/debug.dart", "hash": "d4b68da22867b9c51c88acc54eab3198"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/expansion_tile_theme.dart", "hash": "8166d4859a89eef9e25697932c522bce"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/routes.dart", "hash": "a7ff187b346e6f314bb2bedf9c0dd8b4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/text_field.dart", "hash": "ec1a5a0240e45b10543928ea49e81954"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/aabb2.dart", "hash": "f8fb1733ad7ae37b3d994f6f94750146"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/page_scaffold.dart", "hash": "cf16976568279aae25258cc7b9e13d05"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/factory_mixin.dart", "hash": "25c9f7df4c862471f534b041fc4213b8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/material.dart", "hash": "fac5219bf2b41465062330fab7f9513e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE", "hash": "7e84737d10b2b52a7f7813a508a126d5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_controller.dart", "hash": "3455df89df3647c70f9d2586275523b9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/theme.dart", "hash": "3c637285d0108dc200c76da191387fda"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/geometry.dart", "hash": "bbb69af0d12fcbba46f19dd6c5fbe986"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/noise.dart", "hash": "206b1db3ce5f7b9e5efd220712f8d391"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/icon_theme_data.dart", "hash": "ab91622a9d9c558bb65f0f06b904d873"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/overscroll_indicator.dart", "hash": "935e33f62741393ea855ced5c6e9c109"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/navigation_drawer_theme.dart", "hash": "098ef2cc21af375e75e3fa80f2c8f12f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/flutter_logo.dart", "hash": "817fe242a7b957dfe6339984fe27e706"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection.dart", "hash": "b87bce461399faa5b57c569a2fbfdc0e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/radio.dart", "hash": "7533fd258516268f6207cb0ce376efa4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/icons.dart", "hash": "9dba8176845e78154423d1da2afc805f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/editable.dart", "hash": "88cdfd439287e97071086e1fa9587d9d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/divider.dart", "hash": "7397ee35fbfd4feddf487df2023f0ffa"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/performance_overlay.dart", "hash": "1f718a5e5adf4cd36518d72729c9d228"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/filter_chip.dart", "hash": "4b8af7a5a3f18220de22d881c8b7e441"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/search.dart", "hash": "c70c243948dfe23554cfeab5113a85e6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_selection.dart", "hash": "781c9623c4cd5f2d83a30b1215968fb6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/spell_check_suggestions_toolbar.dart", "hash": "3dc87176030ef89a3793f45a50882148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/utils/math_utils.dart", "hash": "e4ee21048ab83cc50d61ac3784afa9f5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/restoration.dart", "hash": "ee984ad6a59ef4e7fcf5caa40736878c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/lib/plugin_platform_interface.dart", "hash": "8e49d86f5f9c801960f1d579ca210eab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/intersection_result.dart", "hash": "789e79772bba1132b3efdb60636a3ccb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/pause_play.g.dart", "hash": "8ebc4ef8486c9875330658ed1a145020"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/beveled_rectangle_border.dart", "hash": "3792d5222fc5ad4541075e14db5aa3a2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/layer.dart", "hash": "20ce00589bf0dc7a951333cac52c8189"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/logfmt_printer.dart", "hash": "1812a211ce0ad9a2385a310cea91bc01"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/default_selection_style.dart", "hash": "2f21ecaf225265e45135854c47dfed90"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/radio_list_tile.dart", "hash": "0fec103586c995464eb59fb5b8dd805d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/page_transitions_theme.dart", "hash": "25f5ba613ed8a191224946c712d5cf59"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/spell_check.dart", "hash": "8d902c39254551a753b0f6df3ae01552"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/image_stream.dart", "hash": "1a72cb8cfa5f9b9c7f4f702f0a5292ef"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/animation_controller.dart", "hash": "52646767fcdd17ac1eba794fc9233aac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_extensions.dart", "hash": "040a16c5fccfea5a33d4c771c93003c2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector2.dart", "hash": "6a0fa6360b3aca8deb85dc7d88176eb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/guid.dart", "hash": "55bb53dd4f9ed89c9ff88c204b59293c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/eager.dart", "hash": "0571bd14c39812685221406f495590bc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/text.dart", "hash": "fe728acba18d7428b1b408dba5a5b803"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/view_list.g.dart", "hash": "ad6bf1d7b3079f5be69fb40ada4fc145"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/pointer_signal_resolver.dart", "hash": "68be6177d4ad4d0bd0bf7fa94c86be81"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/value_listenable_builder.dart", "hash": "fec9b62fbd9458c01b4e38e6086a12e9"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.bin", "hash": "262e300825694b93ab2f5ea6138a6baa"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.2.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/lib/shared_preferences_linux.dart", "hash": "492280af61b4bca29e21d28db0c2be1c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/text_style.dart", "hash": "9f62141bb6f3cdd428a64a233588e5d7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/file.dart", "hash": "51ffa7b452686eecd94ed080a1da4275"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/icon_button_theme.dart", "hash": "76e270c31be8244f4a49b954bba9c76d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/opengl.dart", "hash": "9e22ead5e19c7b5da6de0678c8c13dca"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/simulation.dart", "hash": "c0fe6462e3a08d6d6afbf4f66130d494"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/bottom_navigation_bar_theme.dart", "hash": "c970404e32ab9a5917d955f66c830b1e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system_entity.dart", "hash": "22f170a8dc9abfac2942555e83589e1a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_map.dart", "hash": "9d273d5a3c1851b0313cd949e7f84355"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/recognizer.dart", "hash": "86c0e6faf3a1e78ccbcd1b895dfd3c3e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/empty_unmodifiable_set.dart", "hash": "d2e49f7a3cc02c7bd120dd5e4b9daa33"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/text_painter.dart", "hash": "b8ca84864a4c809695cac2e6118f48b6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/border_radius.dart", "hash": "bd5608675b3a2a47009cf9b53a271e4b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/event_add.g.dart", "hash": "70b3c5178a2900b73be78d52770fcd40"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/viewport_offset.dart", "hash": "5d25b1e63fc82dde80367f8504514fdf"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/dropdown.dart", "hash": "055bdda380425eebef7db9bc427f58af"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/rounded_rectangle_border.dart", "hash": "65f6b2c928ecfe756bee29f8cb0a7d14"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/card.dart", "hash": "b8c5eb9383c75649a3e11626ff3aee68"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scrollbar.dart", "hash": "5af2ba4c66adca83fd54da05d34a86ec"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/widget_span.dart", "hash": "281220e23e0509f765275e58e072cbc9"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/ios/Runner/Info.plist", "hash": "60e0da506d9f818b1ab4778a06656bba"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.18.1/LICENSE", "hash": "0c3ca74a99412972e36f02b5d149416a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/annotated_region.dart", "hash": "3bc33c65fa44a57d13430fdedef82bc2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE", "hash": "fb92f0b8decb7b59a08fe851e030948d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/ink_decoration.dart", "hash": "0ef0adb529cf9677d4a8905e7fb62367"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/timeline.dart", "hash": "4864ded8675881d0744b52cc5f095372"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_output.dart", "hash": "1cc168543c8f88638826f971d68adbae"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/ui/color/info/color_info_widget.dart", "hash": "bbd77d6150f9e922fef12f4cdbf136a3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/path_provider_windows_real.dart", "hash": "43f4676f21ce5a48daf4878201eb46bb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/grid_paper.dart", "hash": "f76941994ddf30e398313421f1588d85"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_unlike.png", "hash": "f0b3dc3f43c082df311b6f5c54218511"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/navigation_rail.dart", "hash": "cea123784b7b4bc44d3624a318a2367c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/text_selection.dart", "hash": "2b681a07dbff7897aaf25c3a98ceb981"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_button.dart", "hash": "a50a095764fe5bbf31a0c4f513d44858"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/kernel_blob.bin", "hash": "71c78304f80425fb443c1853bc25fbb3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/icon_button.dart", "hash": "d85d68ca27c83726bb65b97c5e1be507"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set.dart", "hash": "0073f703be7f7ddbd7f04d1b740f35c6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/badge.dart", "hash": "9315e6d655576a148588c112dd7bfe2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/dynamiccolor/material_dynamic_colors.dart", "hash": "f0af2ad64175c7ff87ba05ec78b35a30"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_linux.dart", "hash": "a899aa5c1896800f6367cc200a3a782a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/overlay.dart", "hash": "301c6f72b21180a64c4cf00432bbe6a2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/flex.dart", "hash": "3849c7a6889bd7d5cbd63ccc4d7882ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/local_platform.dart", "hash": "9cc2170ec43e47681be6cb2a313ba1b5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/isolates.dart", "hash": "4d78648749025b5ed6c8eb859bbf6755"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/app_bar.dart", "hash": "c8add4e3b990a6c2b4ce0dc09aa586fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/logger.dart", "hash": "610f4d6fd60c125e08d766985d536d52"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/LICENSE", "hash": "1d84cf16c48e571923f837136633a265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/banner_theme.dart", "hash": "15951ad2d184fb64e0327b35f1ce65df"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/icons.dart", "hash": "f97ee374b95ada8d2cf0c6042d5a0f24"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/mixin/factory.dart", "hash": "5b48bba3dfcba0ea1ad63f36aa8bf66c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/list_section.dart", "hash": "e1cd65bf59468ea9d1fb60660616fa1b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.3/LICENSE", "hash": "5bd4f0c87c75d94b51576389aeaef297"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file.dart", "hash": "58edba46526a108c44da7a0d3ef3a6aa"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/debug.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_async_platform_interface.dart", "hash": "03664e80d73ff10d5787d9a828c87313"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/fractional_offset.dart", "hash": "0a2cf42cdd64530e5ca9a120eda90f12"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/interface/platform.dart", "hash": "d2bab4c7d26ccfe4608fe8b47dd3b75c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/desktop_text_selection_toolbar.dart", "hash": "ee50c3d1a9d13caf4352f41121737441"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/advanced_file_output.dart", "hash": "b485ef67fbe801f44114a023c4458b35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/path.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/frustum.dart", "hash": "d975e51852aa1802c81c738dcb4c348d"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/pubspec.yaml", "hash": "42a1622d70e0a0b0979c049f983dcbcc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/method_channel_path_provider.dart", "hash": "77ed8d7112753d0eeaa860ecd9fc5ba0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/image.dart", "hash": "041d53aa49db9369749abcfea41dde07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/sliver_prototype_extent_list.dart", "hash": "5ee48365492a641d8f94b1ee62bb5b14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/sqflite_database_factory.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/tap.dart", "hash": "31f93490acebbfcf1828b7e41599200d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/list_tile.dart", "hash": "edd3a8756a0a528491d366812c6847af"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/dev_utils.dart", "hash": "9a4ee08ca541303a2aee95f83f548ce1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_metrics.dart", "hash": "a7a9eeb4bfc63b4f552162a16b62f70a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.3/lib/src/shared_preferences_foundation.dart", "hash": "db8ef5ac4d806e72f7b356056cb50b1f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/proxy_sliver.dart", "hash": "81f395ba7a262f5e3f75cc8ce6580d0b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/texture.dart", "hash": "888c72929d9b3cd94975f06965e72976"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_physics.dart", "hash": "eb4abf5a7d99a87e391d54b8afa7abcb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/vector_math_64.dart", "hash": "bd1315cfa157d271f8a38242c2abd0d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/path_provider_windows.dart", "hash": "38dc31b8820f5fd36eedbf7d9c1bf8d9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/switch.dart", "hash": "7c09169b6fc94a8220d6ae8726196a38"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/title.dart", "hash": "dee4f18e2804e238c57a305ccd28eb85"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/long_press.dart", "hash": "00e4ae85e23d2af2af563a1d2c7a0222"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/utils.dart", "hash": "2d3b2846d7071fb93d36485c261040ea"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/sphere.dart", "hash": "63473e31f03ea66a38affa41fd783752"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/motion.dart", "hash": "374f899d15352be34ce61fd5243bed08"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/toggle_buttons.dart", "hash": "040083378c2b40cbd207186baa719e37"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/material_button.dart", "hash": "da9ecd9bf1968692f391966d2c3c193e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/raw_keyboard_listener.dart", "hash": "d8f8a80ad0c05f281d58e8f9e20b8b14"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/quantizer_celebi.dart", "hash": "f12f9a9b8bb504f4617bfd1c00d403f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE", "hash": "2d0c70561d7f1d35b4ccc7df9158beed"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/table.dart", "hash": "f41b5fbe98340e131b83b424fc2b0694"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/priority_queue.dart", "hash": "34a4d340931147322eaddc77fdc65c22"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/debug.dart", "hash": "906581419e8d8c283d6bdc2730641ecc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/two_dimensional_scroll_view.dart", "hash": "ee6dc9d3e5d18e671f4e597adac94ca5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/button_theme.dart", "hash": "4370385dd73406a508c5b564963a1932"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/container.dart", "hash": "7354f82aa2935ef7c8faf44dc9a09e11"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/animated_switcher.dart", "hash": "e9dfc595df01641862dabb659e901a9f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/context.dart", "hash": "daeb052f1089d4e84d8a22acf56c1da2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/menu_bar_theme.dart", "hash": "438d55cb1016f68c4db0da87b19ac82f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/time_picker_theme.dart", "hash": "1676e4af87bbf4fd18e1783b9b46f17d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/app.dart", "hash": "8c0ca18cedfd110225dddb6ec6092ea2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/selection_container.dart", "hash": "0b0f625bca76693cdeaa1f4358809351"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/close_menu.g.dart", "hash": "dd134142f6edb06d6ad1ebc0d27fb524"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/batch.dart", "hash": "34c2a18e24c9d0bc442ae2559a4d7479"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/pointer_router.dart", "hash": "6e800790e7858e8e1cdc73c8cc09d719"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/_background_isolate_binary_messenger_io.dart", "hash": "170fe4655f45b54388ab850399d92895"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_macos.dart", "hash": "c290402ae5c6c65e59113f8cd82d3630"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/object.dart", "hash": "daa0c9b859ed1959e6085188a703f387"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/notification_listener.dart", "hash": "11a634821b3bce05dac94f3dabe52a75"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/bottom_app_bar_theme.dart", "hash": "6dc0750e02c1822a0114fe97b5d7d870"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/binding.dart", "hash": "89f6a6a99117495ece645bfc6ba9f455"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/system_navigator.dart", "hash": "b57cf84d042590f93596eb09ceea35f0"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/sqflite.dart", "hash": "b52dd9200a521b214e00ca9f902df467"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/alignment.dart", "hash": "fe7555b691d2ab411773e80a58d30911"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/url.dart", "hash": "13c8dcc201f970674db72fbbd0505581"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/boollist.dart", "hash": "206ef1a664f500f173416d5634d95c8b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/dialog_theme.dart", "hash": "1dfe8c8f0f5fbe0e06eaaf8c02169673"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/env_utils.dart", "hash": "d75f62f03297d8fada84de77f3e92373"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/switch_list_tile.dart", "hash": "843e2da929ed5bc514c40ebd58f6bd46"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/platform.dart", "hash": "1dbfb3bbbab79ec8f59410bd3cfcc723"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/transaction.dart", "hash": "95701ee376845a2050d29814b7acc7a4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/shared_preferences_platform_interface.dart", "hash": "59bb1cba1648db956dccb835713d77d8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_map.dart", "hash": "13c9680b76d03cbd8c23463259d8deb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/score/score.dart", "hash": "58b9bc8a40fd3e2f7d9d380d0c2d420f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/logger/sqflite_logger.dart", "hash": "e7c0fb31f3a1d9bc9291b55edada605a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/context_menu.dart", "hash": "4b0bfd9427f67249b2604de81db1da55"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tooltip_theme.dart", "hash": "1a30ad0a0b06af36509ef6ae4e05be9e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/debug.dart", "hash": "7c0dc3bf37cda1edf1e8a55dbe47f57a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/view.dart", "hash": "6faea0556ba5a7edda9f834d3865ef84"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_ios.dart", "hash": "ef8de33c998d16ee23cc489315035a30"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/stepper.dart", "hash": "03cdfb3ee79e121a869b4140cdd3e64d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/plane.dart", "hash": "f0c6d5d05fbdc95ab84f1a63894b7be6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/annotations.dart", "hash": "b092b123c7d8046443429a9cd72baa9a"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_like.png", "hash": "f213fdd78d02a19eaf3aa5da475faa56"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/scheduler/priority.dart", "hash": "90c1fe2fd81558e20d588ef846f68876"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/performance_overlay.dart", "hash": "f29eddf933f8f61e7c7acaa4bb7acdd6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/box_border.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/overflow_bar.dart", "hash": "5b36aafe7bcee63edc9ed46625146b0c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/colors.dart", "hash": "0ce260ce58b65035779d563a6e2d6150"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/serialization.dart", "hash": "41bd294b2c2eb1b089ab65341e92fd83"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/paginated_data_table.dart", "hash": "86a6fc84462d9d59a64d1c32494e96a6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/cursor.dart", "hash": "5bde4f62a64276d44e1ef4ee3bf194f6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scrollable.dart", "hash": "c9d14ca6f9991efa868f91b7a1e1b207"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/wrappers.dart", "hash": "91e47ed79ad65391642894923c520b26"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/observer_list.dart", "hash": "074b866f17aee09c76583b075e83cb8c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/error.dart", "hash": "b831e4cd07f0e2ad701fdf6ac1dafe19"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/models/color_value.dart", "hash": "119f116134f77ab2e4ba43c3b1af10e4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/button_style_button.dart", "hash": "653503c175d02d3f2dad0509f466b72c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/ray.dart", "hash": "146741f6f87d6612ee7bbf6a6fa9c119"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_delegate.dart", "hash": "91bbd493772cf9df333710badad4de95"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/image_provider.dart", "hash": "6e825e31ade1c31908e3e4587caaac70"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/shifted_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/path_provider_linux.dart", "hash": "b48ba72a2d5d084d297c3d78e351036e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/inherited_model.dart", "hash": "8635fbec89c2cc03404a2a3233d31bbc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/message_codec.dart", "hash": "3b5bc5c0df6b6e1abb26877f612b2242"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/desktop_text_selection.dart", "hash": "d34b1e33e7604b54b656d4c7471ad8a1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/listener_helpers.dart", "hash": "55380226455ea534ad3f21ab09fa4cae"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_group.dart", "hash": "d312511ada938a30bc4aad2884c87e59"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_notification.dart", "hash": "693ed2db852e9a1dab34a89c972f8cbc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/list_body.dart", "hash": "632690aaa6323ed800e388a07c472015"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/sqflite.dart", "hash": "bf6e6dd7438a97863d2ee3666a050173"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix4.dart", "hash": "b5f0b0da99e8a07d58c21ae071800404"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/capabilities.dart", "hash": "4e9429c3bbf61961f9284cb7ae3d432a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/expansion_tile.dart", "hash": "eed2b07419008495c3fabebd91c418e3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/binding.dart", "hash": "832e6a5296a4e1c0e6d2325726b78631"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/page_view.dart", "hash": "1b7059644205db9ec41921ffc1ef2fb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/error_helpers.dart", "hash": "39221ca00f5f1e0af7767613695bb5d2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/desktop_text_selection_toolbar_layout_delegate.dart", "hash": "bd34896b1432d6f707498d3df7a7c3ae"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scrollable_helpers.dart", "hash": "7f8d465c33e616189be8fa5e9da5b2e3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/badge_theme.dart", "hash": "f179cf16ea560111839fc980420e3b18"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/switch.dart", "hash": "745368601fe8f294fe6b1a3a85443af2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/form.dart", "hash": "08c939bc94a2b600f7606d2e148f0b6c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/mixin/import_mixin.dart", "hash": "cd0157df37359679426daecbd76dfcc5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/progress_indicator.dart", "hash": "15c3cb7ae940de581612b4cd74214a4d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/_platform_io.dart", "hash": "2c25776d2e769ec49cc16f5058c5c009"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tab_bar_theme.dart", "hash": "0e1d5d209c6b0c5646684392602d9313"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/text_form_field_row.dart", "hash": "a6e4e91fdeb5158627af1bdd5d0f37e7"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/FontManifest.json", "hash": "dc3d03800ccca4601324923c0b1d6d57"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/checkbox_theme.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/sqflite_logger.dart", "hash": "6745a4321f65340dc91faae80415984b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/dev_utils.dart", "hash": "04094f9c5e24e8353c5133eda3ce150a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/date.dart", "hash": "28e25b8e5d770475e8fbc3ef2fcbe5ba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/bottom_tab_bar.dart", "hash": "b417aa555d4798d16680f756bba71114"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE", "hash": "1bc3a9b4f64729d01f8d74a883befce2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE", "hash": "f26476a70de962928321bf9e80f9029e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/tolerance.dart", "hash": "f75f31535e16b018e2a5f9a968b7254c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/foundation.dart", "hash": "641dd1fc6aaa15f1917eec571d2545af"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/placeholder_span.dart", "hash": "c7c1fab9fded69885fd9c0e76db051d8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/orientation_builder.dart", "hash": "2df422a56d9988b696a9b0950f28bee3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/theme_data.dart", "hash": "bd742ae7660f29302666ae81b8c5b159"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/binary_messenger.dart", "hash": "4796d69d2b7d8c2d0673e26e441f7450"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/clip.dart", "hash": "9f8596ea4f2595f78ea9e55b6c9bec81"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.0.2/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/data_table_theme.dart", "hash": "9385b8aa7f7830b7ff66f3f1c8bc619e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/selection.dart", "hash": "5908f719b79e4fdd5c738de271607266"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/dropdown_menu.dart", "hash": "7dd447188fcdb747898171cfa2a6d0dd"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_notification_observer.dart", "hash": "329b723b2cea0443e5ec2ccfb31fbfb8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/async-2.11.0/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/bitfield.dart", "hash": "d235f51d48e43d80a46b35d3ac1a7135"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_button_theme.dart", "hash": "e06184900e9722a899299b08b5b1d95c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/inherited_notifier.dart", "hash": "f4f97e64864383af2f259063e32bcf49"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.2.0/LICENSE", "hash": "d26b134ce6925adbbb07c08b02583fb8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/floating_action_button.dart", "hash": "996fdaa81271773085d49d29f6170be1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/circle_avatar.dart", "hash": "e94ea5f48a4115e627fc0a77ba9e2ad7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/view.dart", "hash": "ec9af99d83f9d19e39573bec7721fbfb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqlite3-2.4.3/LICENSE", "hash": "f9a7098a030108fba87a81626c482aa0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/platform_menu_bar.dart", "hash": "8d78bcb44cc1a11223e8ff572c929d30"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/interactive_viewer.dart", "hash": "5185157e5b6205b48b1f9ffc2ad14f31"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/input_decorator.dart", "hash": "47e3e77af03873341fc07cf429be6c14"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/button.dart", "hash": "f2d99c1b53271690d8cec6d23ec9190d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/bottom_app_bar.dart", "hash": "17d4a4d8428d920cbae781e95dd47d4c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/viewport.dart", "hash": "82afcbf44c7d906ef6ce3d3e57c7eae5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/scrollbar.dart", "hash": "d4dbf999e326d3847fae5ed4b0889dab"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons.dart", "hash": "8288239ccc449f5dec9f381298c92c1d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/app_lifecycle_listener.dart", "hash": "1f442d376af9a31939dd759498712154"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/utilities.dart", "hash": "3f5e8feebce49c954d9c5ac1cda935c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/triangle.dart", "hash": "7d2bdb4801fc8b3a110f36d5e5fa59f5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/pretty_printer.dart", "hash": "bf2bc3af52875d3e5715ed2dff220c07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/exception.dart", "hash": "5ee7523f60ca432b7896fbc746ea64b7"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/shaders/ink_sparkle.frag", "hash": "36934f3e3db04fae4336b87db7e94399"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/arg_utils.dart", "hash": "9812b8e536c69068c0e5f3d3db20c140"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/local.dart", "hash": "e81341d4c5ee8dc65f89ae4145cf2107"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/fade_in_image.dart", "hash": "0d81828cedbe16ec8c88203bf060120f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/lib/method_channel_shared_preferences.dart", "hash": "513d6195384503beeb7f3750e426f7bb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.6.1/LICENSE", "hash": "3323850953be5c35d320c2035aad1a87"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/animations.dart", "hash": "22a22c06ad83b898ce8a828a28116dc6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/utils.dart", "hash": "727e4f662a828d4611c731f330a3d79a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_context.dart", "hash": "22c35af71293a579bba619b03228367c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/context_menu_controller.dart", "hash": "678c1e389407e5a9419d4d94e5679593"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/decoration_image.dart", "hash": "90cb579a1daad41698062c5ea0c22727"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/layout_helper.dart", "hash": "41cdeeb9167edeb876b8a65fc029e627"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_form_field.dart", "hash": "f04d191a3115ce6ecd1043e08260fc00"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/text_layout_metrics.dart", "hash": "13be7153ef162d162d922f19eb99f341"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/models/color_category.dart", "hash": "00dd4a1d55f2c3b7309d36c15f84f8be"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/tween_animation_builder.dart", "hash": "a03d54e3df9767cfe01d3e7b0ffef92a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/drag.dart", "hash": "63c306461db5c60830794d4def8b7e7b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/simple_printer.dart", "hash": "178f62efb676bb0f4293df1f3f7beef7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.11.0/lib/meta.dart", "hash": "f8f5e53579ca6381f4c92615adca9f07"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/constants.dart", "hash": "9f9b79f577d9fdf4f20c17a26a2f1d57"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/parsed_path.dart", "hash": "cb454929d7810d3ee5aa5fc28283d3fd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/radio_theme.dart", "hash": "b667b9f8acd4c92f97a0f7b908c79662"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/restoration.dart", "hash": "585ea2d3976a9176c7e15d1fc4a7d840"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/physics.dart", "hash": "6e29d5e69c5745a45214fe14da377c1a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/refresh_indicator.dart", "hash": "40ca1f9bce15490ddb287706fc2bd09f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/viewport.dart", "hash": "e797d0f85b6b031854f48a68e6d9f9de"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/semantics/semantics_service.dart", "hash": "4f5b949b0671ed2d759a907e2e26e44b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/sliding_segmented_control.dart", "hash": "c4913601d0f06b5efa8e58e0043ea166"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/curves.dart", "hash": "d4a7f2ab410884c0a4fee855bdbaa7b3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar_layout_delegate.dart", "hash": "942fbfca7541358613467c640e1ca6cb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style/posix.dart", "hash": "5e054086533f32f7181757a17890ae56"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/object.dart", "hash": "bd92baa7cdfddcfce506fa5e9da2768a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard.dart", "hash": "e634ec13f46acee3f021c73b94f973eb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/bottom_sheet_theme.dart", "hash": "87e638fbc5e15e8d93ef84462a09bcf5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/exception_impl.dart", "hash": "7b3fbf91245e315040bd120bc9bf51ea"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/haptic_feedback.dart", "hash": "2a90f95a9de0d2364fee5e1ddbab0c18"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/assets/icons/icon_nav_back.png", "hash": "a221919c48512ad09556491b8e31a91f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/services.dart", "hash": "7be67e467a2435a76928e00ced33ec85"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/search_anchor.dart", "hash": "174fe91458048b500bf41b51cff4bbf7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/draggable_scrollable_sheet.dart", "hash": "0b04e31803c7ab601728c132b449967c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/elevated_button_theme.dart", "hash": "86b06851f3ff5ee17bb39fd0d241cbb9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/bin/internal/engine.version", "hash": "858a7f69db80b53684dfcb991e2f8bc1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/magnifier.dart", "hash": "5e70d1ad692c941ebb265dc966c100fd"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/debug.dart", "hash": "51fa10cf30bde630913ff4c6e40723ba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/live_text.dart", "hash": "04bac565974eab63debf48e12bc971c1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/no_splash.dart", "hash": "8217a1327affdcc17e4e9789ac490e7a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/continuous_rectangle_border.dart", "hash": "eb1ac0495c3dc1aea70fe7237a1f3593"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/converter.dart", "hash": "7d43f36e94debd919ec49446dbfafa2b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/snapshot_widget.dart", "hash": "046ebc8d9f368d710d341c545f0da591"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/scrollbar.dart", "hash": "d62c0d6c414d92a3ad58a9a250f710df"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/decoration.dart", "hash": "de79039aaa3838c467be941ecfadf198"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "hash": "e7069dfd19b331be16bed984668fe080"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-2.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/arena.dart", "hash": "49ca3508224c7b7375228522cf9c6445"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_multi_box_adaptor.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/win32_wrappers.dart", "hash": "af7270fd3861278053b1c45a7b66ece3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/action_icons_theme.dart", "hash": "3fd3c4bcbbf54fbcad3b048e3c89d43f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/debug_overflow_indicator.dart", "hash": "1b1961b095a10353c8aee62967945239"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/home_menu.g.dart", "hash": "f183c429d3db89b9c97dfacaa85f09c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/quantizer_map.dart", "hash": "b6bcae6974bafba60ad95f20c12c72b9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/LICENSE", "hash": "80ea244b42d587d5f7f1f0333873ad34"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/go_router-14.6.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/dart_plugin_registrant.dart", "hash": "44b8efa69ec831d1a0ce74c20ecc27b4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_filter.dart", "hash": "32581c4e1ac594b374549efd0b5f46c2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/collections.dart", "hash": "44bb585dfd77cf40397a9f9c8aabd985"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/image_filter.dart", "hash": "e7651e730f1ce3e0a0b87ac950fcce68"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/typography.dart", "hash": "68516c325dcfad0453598ab2d588112f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_link.dart", "hash": "600a83d8e8dcbc1fde99887eea16f18e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/constants.dart", "hash": "823c66be3a8d17bc0c146c6b7f83062c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/layout_builder.dart", "hash": "188518c8a8bf6f0f55eecdeea64d3d0c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tab_controller.dart", "hash": "da25421fc5112398084443e9a65015c7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/segmented_button.dart", "hash": "4432b24e1e7e05aadc5fb252e5cc2db8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/database_file_system.dart", "hash": "dac02dc6cb13c753a5f3ae19976b1540"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/painting.dart", "hash": "f20f861f36f2bafc27a6cb584a89e388"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/floating_action_button_location.dart", "hash": "38ecc18f28707668984a8d0f1202b928"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/semantics/semantics.dart", "hash": "dc9caaf9a3a97a98d81d1385957d6d76"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/dropdown_menu_theme.dart", "hash": "aeaa12c1af305eb8e588f3b7bec09ab1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/consolidate_response.dart", "hash": "b869c4e930ab3313f9b1d196d532d3dc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/binding.dart", "hash": "d676ab476060bbf63ed4b2a73404ec15"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/action_chip.dart", "hash": "b8dff27edadc309673deb4ee7328e1db"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/refresh.dart", "hash": "2e4e60fbc597ba85e007783888360f68"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/chip.dart", "hash": "4c2d88bbfea191c3ddd64c83ca029945"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/box_decoration.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/obb3.dart", "hash": "5ca0b5786bf63efd4fc72fcecfe1b36c"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/package_config_subset", "hash": "b923328b78d72b7ef2228b9147ab4f1e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/ink_highlight.dart", "hash": "d61278f7eec1a879fa0f11b5cf33bd8c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/constants.dart", "hash": "fb54c03ca4cc99961be9cd8c867fca54"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/style.dart", "hash": "bfb39b98783e4013d9fe5006de40874d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/slider_theme.dart", "hash": "dd3a42ec0aefab635364b2cac24964ff"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/adaptive_text_selection_toolbar.dart", "hash": "4d6b50eea2de3af6818f4a97abd0fd60"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_tonal_spot.dart", "hash": "834754ed5fe3c15042decab118b4e3b0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/text_formatter.dart", "hash": "e5d5f135576462a38ec6f6b161926a8b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/semantics_debugger.dart", "hash": "1545481e91de2f21c331b1c7e4fac5e2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/inline_span.dart", "hash": "b64689c4c04c036a508a9c8c2dec3ef5"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/colors.dart", "hash": "5ed8acdae7dd3501b64b0ff3e33c1f45"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/drag_details.dart", "hash": "55bbb2ca116069540b7f8cd0c0ee31f4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/LICENSE", "hash": "e4b7798d5e152d20a241f63077e24db0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/undo_manager.dart", "hash": "51ee7865e1efda37cb04af7ecaeba2b0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/linear_border.dart", "hash": "ef23db76fa59185633611b425b8ff474"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/platform_channel.dart", "hash": "0ff59dce8859893f47f9837f958708a3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/multidrag.dart", "hash": "0c5fcc20849825e27f79853e62c1d11b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/quantizer_wu.dart", "hash": "c0da8171c63f0ab4e822dd094fc2c595"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/characters.dart", "hash": "43268fa3ac45f3c527c72fc3822b9cb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/multi_output.dart", "hash": "8a8ec5edf7a4c3d3a3598480901db44c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/grid_tile_bar.dart", "hash": "4f36e38eaf3608ec18c70c13942510bd"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/gesture_settings.dart", "hash": "e2d2ec7a244dae1f8f4747fcac597627"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/hct/cam16.dart", "hash": "ca959e5242b0f3616ee4b630b9866a51"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/rotated_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/localizations.dart", "hash": "cd4b51cbe18d38dc375b7372b11aff1a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/animated_size.dart", "hash": "df31728ed88b16add6c9b99b23857361"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/reorderable_list.dart", "hash": "03d0baed92a212184bf517ec662b8520"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/animation.dart", "hash": "3036308b09569827b81d84d509d6a912"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/friction_simulation.dart", "hash": "d97019cfa3be6371779fc0e65f2bc118"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver.dart", "hash": "af15a30c10c724385a0f1fde46dde974"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/autocomplete.dart", "hash": "a739ec5b459625f3e03ec4b6e8dd7e88"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/grid_tile.dart", "hash": "b526e1fcb69f0ca9df233cd2fb6e69a9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes_dart_io.dart", "hash": "9df03a340058a4e7792cd68745a4320c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/table.dart", "hash": "aa6152a8dc858cd16cf240ebfa31d605"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/dynamiccolor/src/contrast_curve.dart", "hash": "970198d36bc485255b471b6a9dfa1312"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/semantics/semantics_event.dart", "hash": "0fa850c6546bf973cf791a2fc27c0534"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/elevated_button.dart", "hash": "045ae700e5001de086bfab3044466bbc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/search_field.dart", "hash": "f85626fece72b362ad0744ec755d5fcb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/system_channels.dart", "hash": "5d5d94746447a0e21d783bf82d9291c6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/sqflite_android.dart", "hash": "ec562f20505ab846c07aae091a58b990"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/scheduler/service_extensions.dart", "hash": "6656ba0c69fefef80b8cae101896c029"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/button_style.dart", "hash": "2fe4913adcdd44539f979ae3be0a7e2d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/shared_app_data.dart", "hash": "496982c4b90528a5360d8064ddd1373d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/date_picker.dart", "hash": "0e988649b25da080c48f422e2e3e3ff2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/implicit_animations.dart", "hash": "3190cd65255175d583194e5636b25240"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/path_provider_platform_interface.dart", "hash": "09b3f3b1ef14ce885c016f2eba98f3da"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/temperature/temperature_cache.dart", "hash": "a6350a577e531a76d89b24942fca3073"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/algorithms.dart", "hash": "5fac07b9706002db32a4c5f6698cea58"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/framework.dart", "hash": "edb4bcae7097b4235edbfb1e881cbf26"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/menu_style.dart", "hash": "e315980000514ac28dc060da5f362eda"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/navigation_toolbar.dart", "hash": "f24fcd5c1e02f12bafb7b9111fc130a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/grapheme_clusters/breaks.dart", "hash": "359388897ae53df8791213c31ef05fe6"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/flutter_build/dad7ba8c6d34d1cd7d9334760a838f26/native_assets.yaml", "hash": "e7fd2fda36f01436b831ca47fe61fec3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/notched_shapes.dart", "hash": "86aabfcf6259436baade90d2f7783fb2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/_isolates_io.dart", "hash": "adebf857ce52e3d3b47c77d01ccf7f8b"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/AssetManifest.json", "hash": "dfa09cc3e89296144e5c0f5cb9b321e4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/single_child_scroll_view.dart", "hash": "2b69debb765c0b9587f6cee57867e5e3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/sql.dart", "hash": "9ab11d900c41a880b39e97693f383b5d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/drawer_header.dart", "hash": "1786653a5a86ec6255f79137a3a33755"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/form_section.dart", "hash": "d3761fd4134eae426ccfcf45d60c0f29"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/hardware_keyboard.dart", "hash": "b49b3c3a69b2c858d3f9034f72dc7716"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/text_selection_toolbar_layout_delegate.dart", "hash": "5337b24e9e73fe2ab3b2ddbf7444febe"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/edge_insets.dart", "hash": "5cbcf674a0513b9fa147d62eca14a6db"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/async.dart", "hash": "9576c2034386f2fd9f7063b332f45779"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/inherited_theme.dart", "hash": "97af54574da94dbb0a8b5a5549e954b3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quad.dart", "hash": "739bb2e85022ddfb653590b93216942a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/memory_output.dart", "hash": "54d0bd1fab938813ce3076758ba7a1cc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/sliver_persistent_header.dart", "hash": "9d63de715fbdfcbad9064ab771762145"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/heroes.dart", "hash": "97a4e8b47f2da4a5716660a8a556e340"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/src/testing/fake_platform.dart", "hash": "f1a57183b9d9b863c00fcad39308d4c1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/sqlite_api.dart", "hash": "9442b7f8efe89c42cd235c4047480ce4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/quantizer_wsmeans.dart", "hash": "6c6dfd5ba4546c1f32201555d6cff215"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/font_loader.dart", "hash": "8a899256e5ac320579b269ee9b2567a8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/lints-3.0.0/LICENSE", "hash": "4cb782b79f6fc5792728e331e81a3558"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/palettes/core_palette.dart", "hash": "d35b72b249d19f54a4cd6f22ff3299e9"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/flutter_build/dad7ba8c6d34d1cd7d9334760a838f26/App.framework/App", "hash": "5a736e12e18ecf625a597edb31cf59e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/link.dart", "hash": "1f334b50f4df781bbbfab857581c3540"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/sql.dart", "hash": "597e7b293e2531edc3ef788375e11c67"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/utils/utils.dart", "hash": "6c479e0fd2351de96aa7368a1bf8f8ac"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_random_access_file.dart", "hash": "8584e5707c45dd6bdd567a10dfd8cd0d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/navigator.dart", "hash": "cb0a7f50ce7b5d71738c04b99f9fc7a7"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/Info.plist", "hash": "5eb1ee18836d512da62e476379865f8d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/disposable_build_context.dart", "hash": "12613a72d4478ab485b022196d647b8c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/filled_button.dart", "hash": "8d215ed35f2c055e2ba21959719eb190"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/text_theme.dart", "hash": "920c7e9b7555e2d50e6462302f34c766"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system_entity.dart", "hash": "67918403456e9e1c17b3375ea708292c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/scheduler.dart", "hash": "95d8d1f6a859205f5203384e2d38173a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/segmented_control.dart", "hash": "71283f289f895e378b8df2a3c5e2d172"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/common.dart", "hash": "e3d303d4dcd1d8fda43af45ee51149d1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/path_set.dart", "hash": "1b20a6e406ca8e79675b2ebd9b362d10"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/sql_command.dart", "hash": "4e7b4cf98b7ea45960f7d79fffac5705"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/list_extensions.dart", "hash": "9f8b50d98e75350b41d40fee06a9d7ed"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_printer.dart", "hash": "4576043706f693ac8efde372e73b23de"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/hct/src/hct_solver.dart", "hash": "b972c32590c642256132827def0b9923"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/shadows.dart", "hash": "18149c55b175306f694fd72c293e8a4d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file.dart", "hash": "3e30d0b7847f22c4b3674358052de8b5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/constants.dart", "hash": "8865f4ba0316aa33cd4b8e48694ae276"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/material.dart", "hash": "a3b21f145adf2afe75c1980a1396c2c4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/variant.dart", "hash": "8dea906a9b8773920b6d1ccea59807bf"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/nav_bar.dart", "hash": "ebeb1331c173983cfec02972991b6d90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_selection_theme.dart", "hash": "eb2a941e76ef3aaf9ff856a5d93e0f7e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system.dart", "hash": "3120b9b427a566f796573ee37167c026"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/menu_theme.dart", "hash": "887a4888dd10dc19020553757a12bf31"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/color_scheme.dart", "hash": "b183ccd698383c8d2fabcba5680f437a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/animation_style.dart", "hash": "01c4bd11b0509e27ec93e35976a50053"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf8.dart", "hash": "3b21907d68a2e99afa8e4103f6a72f78"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file.dart", "hash": "1026f587763defb6fb1eec88c2154a3d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/shader_compiler.dart", "hash": "fbfed59bfe96f6ffb8051617c1201f35"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.1/LICENSE", "hash": "175792518e4ac015ab6696d16c4f607e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme_content.dart", "hash": "884b7de681a1ac230007da646af44e35"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/service_extensions.dart", "hash": "0eef32ab9b2cf423c48e89f2dcd9bd6b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/box_shadow.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/database_file_system_io.dart", "hash": "35c142ea243059f941a4a896a8e053ae"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/tween.dart", "hash": "2075a8e4fff779744cd4a576442b1262"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/scheme/scheme.dart", "hash": "f404c13ab6ba7fca8a465852af425026"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/platform/platform.dart", "hash": "17488cbfc8b9ee2e6e5ba0229d7c21a1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/basic.dart", "hash": "b366f4cce7fd8db6d822f752d0d6ce2e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/lib/src/reentrant_lock.dart", "hash": "40d289ced2d26f141e847fa5544f6c2a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/lib/xdg_directories.dart", "hash": "737107f1a98a5ff745dd4e3236c5bb7b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/material_localizations.dart", "hash": "83065272a488a8eb0dabc21d0114fb44"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_home.g.dart", "hash": "24cdd2cb365ef36394210a26c9fb1dda"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/data_table_source.dart", "hash": "0145529858ad246065f7145bac7aef99"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/functions.dart", "hash": "a3aa36a805436731699f39e6bf524087"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE", "hash": "3cc5c8282a1f382c0ea02231eacd2962"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/memory_allocations.dart", "hash": "75c38766ddb6a4505dc9271c6a9fec49"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/widget_inspector.dart", "hash": "64ce38ccea1c45a2ab535b999e251f05"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/browser_context_menu.dart", "hash": "6a35dac0f777e7dd228bde492c4089b2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_field.dart", "hash": "d76bcd0d6504994590172264053ca732"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/focus_traversal.dart", "hash": "0f48c44ba5136e806f75ce99fb4d88a7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/spell_check_suggestions_toolbar.dart", "hash": "b90ed671e7e766e8a27de8544ddbdcf4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/list_tile.dart", "hash": "b4178665f99545254fce62169f1c4e23"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/search_bar_theme.dart", "hash": "598356a65083486d0251d63737c2ddb7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/icon.dart", "hash": "20fbf0ae1f42909e7806add12b2c6e3d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE", "hash": "753206f0b81e6116b384683823069537"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/sliver_varied_extent_list.dart", "hash": "4458df08a3d8e66479932b35c2d71a2c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/hit_test.dart", "hash": "25e5e0cc1932fa7d08cf93c8cfdf9d21"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/scale.dart", "hash": "9ea24c796d0daa458caeafe7ffc3d0e4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_file_system.dart", "hash": "06c73ad137e5db31d7e6ba4258ac13c7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/spring_simulation.dart", "hash": "7a33e5f1b83326a3d67e39599e2c2b4a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/logger.dart", "hash": "0abc184f4138b805c17d7e37d675520a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/animated_size.dart", "hash": "287056f426be61b8924b601f118cf31a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/filled_button_theme.dart", "hash": "9fcf9265f470f44989cf4da88dd7cc0c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/lib/synchronized.dart", "hash": "397d873eb26cf7e1df85933e16c7580b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/custom_layout.dart", "hash": "cb19324d7400b29cab877e6fd6aa0289"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_event.dart", "hash": "30c8223ffe2768eb8917d150bb063a8f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/text_input.dart", "hash": "f3cba4e3673522d0d6c0d42e74d1582f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/proxy_box.dart", "hash": "********************************"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tab_indicator.dart", "hash": "2272b9de3a74259073961e43f74255ee"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/unicode.dart", "hash": "8b525140e1bf7268e1681a62c7640eea"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/focus_scope.dart", "hash": "9cbc35b93da5091ed7d57a20fad464a2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/snack_bar_theme.dart", "hash": "56eac5b5f0f3141bda708ab78e3c56d8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/service_extensions.dart", "hash": "540497224c553a9b08b20397bd78ef69"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/autofill.dart", "hash": "8375d1d56b0d9f7e75e470ca60beaf16"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/text_selection_toolbar_text_button.dart", "hash": "a0a1a162853c04dfcdb92b1a910488b7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/action_buttons.dart", "hash": "5bf33e09bfd444fd925056c612bf4394"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/output_event.dart", "hash": "afda74edd611c35dd0a44e3028c7ece8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/navigation_drawer.dart", "hash": "5e41588086c994904d648bb5e82fd4cf"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/drawer_theme.dart", "hash": "23091b8eb525a6d00f8ec4a0cb84033f"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/dao/color_value_dao.dart", "hash": "abb362dbe0705e3b21e700a4eb93784e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/placeholder.dart", "hash": "08c2e37a2ae282c740bfe820f238eca1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/strut_style.dart", "hash": "26f96fbcc1a3a55b5dd06a311804a5d1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/automatic_keep_alive.dart", "hash": "df5d5624461163b37f494b57a3f5e9d8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/text_editing_delta.dart", "hash": "0ff710795faaca3a5ea7480cf2471807"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/popup_menu.dart", "hash": "76ae6acf37633180bf954a7b076c6228"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/semantics.dart", "hash": "dfcc453f5331ec6f2c70cda394065785"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.2.2/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/scheduler/debug.dart", "hash": "408f65573acf05192ab1153639ff17fd"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/monodrag.dart", "hash": "5ed4f9272065940a4c3069d754457897"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/lib/src/utils.dart", "hash": "54cdf33f86f9167860cadda2f583009e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/modal_barrier.dart", "hash": "4c0d1712c28161aae922d6fb6aa513f3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/node.dart", "hash": "dff97db228356561674b5f690cd54f41"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tooltip_visibility.dart", "hash": "5f94dbea71a53ba72600c479a41fa013"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_configuration.dart", "hash": "f9deeca4d9ec203c0e331528165f76ae"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-2.0.1/LICENSE", "hash": "f721b495d225cd93026aaeb2f6e41bcc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/keyboard_inserted_content.dart", "hash": "f27209609f9689165f058b3ca18165d9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/stream_output.dart", "hash": "b0ad7758ab1a2dc1b0b8bd30c1978d47"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/reorderable_list.dart", "hash": "1b5f98a73b98765142bb94d368acc45b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/arrow_menu.g.dart", "hash": "3ec0013bd7ba2e0f89cb963f867f0d96"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/card_theme.dart", "hash": "46ffe5265ab96981a4304879f9999d5d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/preferred_size.dart", "hash": "d498388a21cc769d98cf664c575d4e04"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/basic_types.dart", "hash": "dc3f11d3897eed163f0230db55174839"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/pop_scope.dart", "hash": "ef7bc60fd57127376e0ec923575d05a5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/binding.dart", "hash": "c9656a0e9f8cac47f11d653265531def"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.0/lib/src/internal_style.dart", "hash": "974d0c452808a1c68d61285d0bd16b28"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/list_tile_theme.dart", "hash": "b1bcaba1089c81f8ba7f22e89726597b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/input_border.dart", "hash": "4283db5afef22d45089f49121b207169"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality_set.dart", "hash": "4b5d82ddeb09bc46ae0e980616ce0109"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/sql_builder.dart", "hash": "389352f8e1ecdf1332ad5bcb395bf9c1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/semantics/debug.dart", "hash": "7f6c267be8df852637226ab1708d5417"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/contrast/contrast.dart", "hash": "0c9bd1af5747fd55e7488c731ad32dee"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.1.0+1/lib/src/basic_lock.dart", "hash": "3139c281b49e6e77c06b3bda8503b526"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/color_filter.dart", "hash": "89862172ecfdefb923b68111e9a86fa1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/texture.dart", "hash": "3c7543874ccaad16712efd4e0249db70"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/image_icon.dart", "hash": "479493da08b4e2137fc162ff23bef99b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag", "hash": "a0e89676ccae6cf3669483d52fa61075"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/dual_transition_builder.dart", "hash": "2570eaf33e6ce252fa201989b9ee6af8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_simulation.dart", "hash": "366aa23421c294b9ad3fa22271afbdb3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/checkbox.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/printers/prefix_printer.dart", "hash": "129f33e0f404d9fe5ef3eb75dd7762e6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/utils/color_utils.dart", "hash": "0938e0447f447ceb7d16477a0213ce2c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/image_cache.dart", "hash": "0888cceb3cbbaaf124ef21c54e22b7ca"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_fixed_extent_list.dart", "hash": "e1354d9ab71f5fb4da9f34e6196ce536"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/bottom_navigation_bar_item.dart", "hash": "3367bc96d45dd306d29483fbf6e41b1f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/tap_and_drag.dart", "hash": "ae5c37e05b62c500ad65eaff43e36df0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_view.dart", "hash": "98fa75fc11e8a325eab7b9cc0d3bcb3b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/stack.dart", "hash": "b56817683aed63ee1b851e568acd5d5f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/io.dart", "hash": "119ed2f372555dcadabe631a960de161"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/app.dart", "hash": "cdd01e9494d6d9702041cc7582346997"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/file_output.dart", "hash": "7dbee69bb2d6088496e7d7bbdde1ccc8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/banner.dart", "hash": "295a212bb4e6429c284f6fccd59886d5"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_like.png", "hash": "f213fdd78d02a19eaf3aa5da475faa56"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/outputs/console_output.dart", "hash": "3430401759c3faf2891f666c719a4c18"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/extensions.dart", "hash": "38e17b28106d00f831c56d4e78ca7421"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/calendar_date_picker.dart", "hash": "0d385eba2511d02a03ba8ed2756bb210"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/open_options.dart", "hash": "0c3c36af8e9790ab80e8790b68cd84a8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/thumb_painter.dart", "hash": "c1a40cac1d97f19b1b786a7f44389768"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/image.dart", "hash": "20997c40dd025d8fcccc86100052da69"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/status_transitions.dart", "hash": "d37e33aaef71722417cb64537e97092d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/lib/src/folders.dart", "hash": "4bd805daf5d0a52cb80a5ff67f37d1fd"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_position.dart", "hash": "30b5e01571f0a72801bf3c443de5abe0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/visibility.dart", "hash": "043377dddf07af1face4788c64ab583f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/error_codes.dart", "hash": "3e82e75a5b4bf22939d1937d2195a16e"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.1/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/add_event.g.dart", "hash": "dd25c518d50a5334f0a231570f7c919b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/box_fit.dart", "hash": "********************************"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/arena.dart", "hash": "b9bf4c34257ba7cad08d8c7092c46e35"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/dao/color_category_dao.dart", "hash": "e572ea987797012c8c0f776c804fa21d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/_bitfield_io.dart", "hash": "5bd42c05ffd396b9c61d5a82739deea4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/sqflite_debug.dart", "hash": "ae7e464ca638c7f5aeb5c9d15eadb1ba"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/asset_manifest.dart", "hash": "346147befca89b339e10bfa134d546ff"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/clamped_simulation.dart", "hash": "a753413d3971339169c4a103d7ee3f6a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/stack_frame.dart", "hash": "240b18e26910e3eb4db6ebe62df2daf7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/scroll_activity.dart", "hash": "f5b4267f1c1f72ab634a2be53517d1a1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/text_selection.dart", "hash": "de16603e53f7e3765822ed64efe93aa9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.0/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/queue_list.dart", "hash": "02139a0e85c6b42bceaf3377d2aee3de"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/nested_scroll_view.dart", "hash": "3effcc99e92068d2d20cef50610395f6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/data_table.dart", "hash": "9dbbce5cb8fe9064410abb30cf56aea7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/clipboard.dart", "hash": "2a64735d53a1dd225670c23206f09e60"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_more.png", "hash": "ce00be35e2da59233b555ca38d60295f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/editable_text.dart", "hash": "f1656b3009488188298548321d3c50fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/constants.dart", "hash": "aa4b5c0cdb6a66685350611b29ca9d38"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/ansi_color.dart", "hash": "2008a57b1ec04a349e6e8c7563f41418"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/constant.dart", "hash": "176c6b2c4f4e2d64cd55df2a0dabe5e5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/raw_keyboard_windows.dart", "hash": "2effc4c6e3d2b0fc9ba8dd05558efd9a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/physics/gravity_simulation.dart", "hash": "b6e95190f367a85405fef52f113a1a87"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/vector3.dart", "hash": "478e1071c9f577b6cabb8d72c36de077"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/combined_wrappers/combined_iterable.dart", "hash": "67d16e841606c4e5355211fe15a2dbfd"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_arrow.g.dart", "hash": "280f78984a3d21c2b797d427c12b4c4e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/menu_button_theme.dart", "hash": "21cd40fc2ea0defcdc048d54b77722c9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/context_menu_button_item.dart", "hash": "34517b36f5fc8d574ff2ffaadcd2b9a0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/team.dart", "hash": "61fc5d47f00a6477275a3102fea8cbf1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/assertions.dart", "hash": "0d5d2bde5f20414e5636ab4a16342f6f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/navigation_rail_theme.dart", "hash": "34ec8e649166b192586b754ce67094da"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/.dart_tool/flutter_build/dad7ba8c6d34d1cd7d9334760a838f26/app.dill", "hash": "71c78304f80425fb443c1853bc25fbb3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/rendering.dart", "hash": "b153a5b28798e7367bc3c5844e896a4a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/time.dart", "hash": "7237f5e7e282aaeee8994aeb5f027835"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/date_time_format.dart", "hash": "a2aff0416ed5e953933c559720b669a0"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_fill.dart", "hash": "09503472662f4997a358debd3401b6c3"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/characters.dart", "hash": "188d03c92376ce139ce247b0f9b0946e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/debug.dart", "hash": "8420732db031571ec52c0df848b35179"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/ios/Flutter/AppFrameworkInfo.plist", "hash": "5eb1ee18836d512da62e476379865f8d"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/sqflite_plugin.dart", "hash": "ae3622db94fb8368f3577f6e71f3ea4f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/force_press.dart", "hash": "bd21408997d44d0bd83cf6d38bf3d2a2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/focus_manager.dart", "hash": "fadb96606ff5b8ff66a461663104cb3e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/key.dart", "hash": "35c3a0e09f2dcf608b806f259c306883"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local/local_link.dart", "hash": "733eb3422250897324028933a5d23753"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/selection_area.dart", "hash": "a1e443cd18f204a8c199864997e17d06"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters_impl.dart", "hash": "3bb0652e163327c58784ce2a2b882a7c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/basic_types.dart", "hash": "60bba52f260b8b776a30061eac93a639"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/divider_theme.dart", "hash": "b794bf7c553a2a0acab8dbfef6b0af0e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/tab_scaffold.dart", "hash": "289bb6d409fa955664df6a7dbc9b7fcb"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/menu_close.g.dart", "hash": "fb3f068735531a31f3d1253216051136"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/animated_icons_data.dart", "hash": "ac08cb84358e3b08fc1edebf575d7f19"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/material_state_mixin.dart", "hash": "d9f9f2488723c1e03b8804bbeb41be03"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/bottom_sheet.dart", "hash": "326012c2f571548eb211cd29915a884d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/restoration_properties.dart", "hash": "e438b8b77c0b056309e25325952b64f6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/binding.dart", "hash": "8bf0e98ee0f944f8651ca31601f92393"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/widgets.dart", "hash": "a400d116543456d9b2e329c2358ba467"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/drag_target.dart", "hash": "3b9095f6b0fce35e4096f5e4e21464be"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_padding.dart", "hash": "77314745e67d324fb766054a8dc1e1e5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/platform_view.dart", "hash": "d40e2e79e07bba853fe73244a275edb1"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf", "hash": "b93248a553f9e8bc17f1065929d5934b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/quaternion.dart", "hash": "698a6fc4361dd42bae9034c9c2b6cf7b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/icon_theme.dart", "hash": "8df5a0fc260d13ce415e2262527a1f8c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/flavor.dart", "hash": "4744aaec510cd9c8b07ca129362b8fb9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.11.1/LICENSE", "hash": "3c68a7c20b2296875f67e431093dd99e"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/resampler.dart", "hash": "780826ab1f1e8af513298cd5b5bca297"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/list_view.g.dart", "hash": "0eae8cad9d933f0478d8387400def317"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.16+1/LICENSE", "hash": "e9f463669bd6dfea2166dcdcbf392645"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/floating_action_button_theme.dart", "hash": "faf51c4fe1dc7af7fabc7c78a960305c"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/Flutter.framework/Flutter", "hash": "96b37e2ced48d4a51e2820efce897578"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/lib/platform.dart", "hash": "cbf041463d4a85115a79934eafe8e461"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/union_set_controller.dart", "hash": "f301af2d0392296f456363085becbf47"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/wrap.dart", "hash": "598204d31b45eae26dc3dc3d36420368"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/app.dart", "hash": "91c2cdd34c3ac5cb94093144569876f6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/asset_bundle.dart", "hash": "b047935f4e0aa6401d249fbc99339702"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/gestures.dart", "hash": "55324926e0669ca7d823f6e2308d4a90"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/quantize/quantizer.dart", "hash": "db799bf48af97b7c0edc93ad96b4a6da"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/menu_anchor.dart", "hash": "5ba6c36fb4a5a147a5d048e78e065148"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/material_color_utilities.dart", "hash": "2e3caf2b9f2375f9a8ec8c3b9bc02db5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/mouse_cursor.dart", "hash": "d31de809d9c7010ffc68a1020a40d7eb"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/dynamiccolor/src/tone_delta_pair.dart", "hash": "f5b38c21bf580c89610a8b58c65aae00"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/database_manager.dart", "hash": "6c9355097d7e93b1e607db71c28da043"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/table_border.dart", "hash": "dca5469228edb0d963377ab916d2c997"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/shortcuts.dart", "hash": "0ac4d7864e3d42f0284d6ebc1366f95c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar_button.dart", "hash": "98777caf8262f9c058d8ce5f6bf47d17"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/_platform_selectable_region_context_menu_io.dart", "hash": "61af6ead2e2dc04677bcfb8c0c2104ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE", "hash": "a60894397335535eb10b54e2fff9f265"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_directory.dart", "hash": "18b0559a8cbfb3b3a3d34bbbea4669c7"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/image_resolution.dart", "hash": "81acf9352ae7f271e6cc7588ec824550"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/icon_data.dart", "hash": "bf4d44ff5dca3de072782665509d0a7b"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/build/ios/Debug-iphoneos/App.framework/flutter_assets/isolate_snapshot_data", "hash": "2b2cfdd3453119daea4e56f557e7b959"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id_real.dart", "hash": "0e5b422d23b62b43ea48da9f0ad7fd47"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE", "hash": "7b710a7321d046e0da399b64da662c0b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/scheduler/binding.dart", "hash": "f9d1fb38c33fb276cab0faf69205ca91"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/date_picker_theme.dart", "hash": "516e37cf5b02c6b104c543a256da862f"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE", "hash": "22aea0b7487320a5aeef22c3f2dfc977"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/message_codecs.dart", "hash": "8de7e97a8b3b39d88780dccf2065c82a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/radio.dart", "hash": "e2035c6e909345a08baf7c6708ebe6ef"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/pages.dart", "hash": "cb78468217bf6e506aad09e8f2e52a78"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/diagnostics.dart", "hash": "d8366bd9aba031762d74c53779756bc9"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/web.dart", "hash": "d7c63cf2f303b7a0aef972ee03d3c7e4"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/form_row.dart", "hash": "34b8b9a525c2edf0191fbd9f925005c8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/decorated_sliver.dart", "hash": "3b268140bc87b4625bd607367cc665cc"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/lib/src/get_application_id.dart", "hash": "32f5f78e5648f98d8b602c6233aa4fc5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/segmented_button_theme.dart", "hash": "9455a98e85614ce00fc324a0fe6f0318"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/list_wheel_viewport.dart", "hash": "1ed64ef2cc7c92446dfb027b39f4709b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/animation/animation.dart", "hash": "ff2a1feb42ebd1c8121ce3d89eec4f04"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/slider.dart", "hash": "780e812784239c38505da95b65db79ce"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/unique_widget.dart", "hash": "3653d3be1eb70d635f7e3f81ecfa147f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/tabs.dart", "hash": "5ab5c46ec0590f245145cd71817e3bb2"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/ffi.dart", "hash": "ae66b0cbdfe2e2a5a99c5dfa48fd5399"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/media_query.dart", "hash": "278d25b9db2e1fd61eeea2d875e50591"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/date_picker.dart", "hash": "b56bb83bdd3aaa2695a2de4f698ca489"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/log_level.dart", "hash": "4c243a6ca83ee01bb17db0d0a77c681f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/dialog.dart", "hash": "9c8235842ee1d82960fd4e90f7520f84"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/collection_utils.dart", "hash": "2b1435db1169d8d724de87f5054c89c8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/spacer.dart", "hash": "31caf5d9d4f0d5e2b373a2bf368290d6"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/events.dart", "hash": "9f62ee47ab4ffadcfc04adb20fa34436"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/mixin/constant.dart", "hash": "84fdc97cdb402f94c301f5154682112f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/image_decoder.dart", "hash": "32d8ff829d8956046c0a91c8ae4160a2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/_html_element_view_io.dart", "hash": "54d59a18ed489222e79e19304ca89cc9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/undo_history.dart", "hash": "f9730d149cdfba55f278c60f7a654b1c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/bin/cache/pkg/sky_engine/LICENSE", "hash": "530ebe9fdfebda94f0447a6a38a62f16"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/keyboard_listener.dart", "hash": "7e7b2010d6453107351c17753d81b0b2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/borders.dart", "hash": "067a882d20a84906270e5703c025be7a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding.dart", "hash": "328ff975234df68963cb19db907493ff"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/path_utils.dart", "hash": "e335de991d295627ccaabe152db13f68"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/forwarding/forwarding_file_system.dart", "hash": "c23a0415bdaf55efdf69ac495da2aa9b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/lib/src/enums.dart", "hash": "f4b67c136a2189470329fd33ebe57cb3"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/system_sound.dart", "hash": "2dd7e3b55dc8a0ddfeee22f0119b0082"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/equality.dart", "hash": "4cbe8ed92ec76b5cd80e685ba71acdb4"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix3.dart", "hash": "447b270ddd29fa75f44c389fee5cadd1"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/display_feature_sub_screen.dart", "hash": "cc7fe333bd38bcbd6ef31e818b3eb836"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/tweens.dart", "hash": "959489b18fda284c434701586b43c66b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/utils/string_utils.dart", "hash": "603b7b0647b2f77517d6e5cf1d073e5a"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/services_impl.dart", "hash": "a6d82f072fbaf76b1276861d20c1b788"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/material_state.dart", "hash": "9835775e78b3addd9abcc5bdc3424821"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/utils.dart", "hash": "fe2489ea57393e2508d17e99b05f9c99"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/safe_area.dart", "hash": "366f1ebf48ef3c69b4e7a9ddcaa8f3ca"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/sliver_layout_builder.dart", "hash": "e7d84c68f69f7f105e4acca6946ded83"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/user_accounts_drawer_header.dart", "hash": "9ac903cffbc9fc815782d8e0bcea7e64"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/custom_paint.dart", "hash": "be92775b7801edc8607dd105e6d7a8bc"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/_timeline_io.dart", "hash": "90f70ffdd26c85d735fbedd47d5ad80b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.3/lib/src/utf16.dart", "hash": "07d628617431f09942070c95c65d241f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/snack_bar.dart", "hash": "2e9b2bbfe8a97fdbf58b4c9f37ff9083"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/lib/src/vector_math_64/matrix2.dart", "hash": "7f164e577cfcf8c8295947195cde2a7c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/text_boundary.dart", "hash": "51f2a9874a4ed255b69aeb0c3feb1903"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/keyboard_maps.g.dart", "hash": "6b92d8f12a7fb46649297e25d2cf2b34"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/tap_region.dart", "hash": "27c89ad8eed09e8c845b2bd9e8a2342b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/lib/src/filters/production_filter.dart", "hash": "d455a0ea71515758776153cc65cb1978"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_nav_back.png", "hash": "a221919c48512ad09556491b8e31a91f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/ink_ripple.dart", "hash": "75112aa4d0b55ffd5ebfceaa75a8be44"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/button_bar_theme.dart", "hash": "470fa5d9df9b39b367a9ec36db367efd"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/interface/file_system_entity.dart", "hash": "04e7480fb89755fcc5f64f7d80ca610f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/foundation/synchronous_future.dart", "hash": "b2516cc7704e0c10a5f1d777ac857ea6"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/dislike/dislike_analyzer.dart", "hash": "d7eb1678ec74acd9857a4193fd62ed5b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_list.dart", "hash": "86ba004de80b95197e3dbcab1233743b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.8.0/lib/palettes/tonal_palette.dart", "hash": "4b7c2f9cc99067bb99ceda29da4c8362"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/lib/shared_preferences_windows.dart", "hash": "2bc47cc0ce47761990162c3f08072016"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/LICENSE", "hash": "d2e1c26363672670d1aa5cc58334a83b"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.4/lib/src/factory.dart", "hash": "90c345ec396d7f892f524ce659aa9b5b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/button.dart", "hash": "e70a2bc670515580f50a12d2a0992c1d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/selectable_text.dart", "hash": "b2ba5220388075c8011877ca1ccf4378"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter_tools/lib/src/build_system/targets/ios.dart", "hash": "6279ad251eae95beb3b08b5633a81ce8"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/lib/src/backends/local.dart", "hash": "22b26473ffd350c0df39ffb8e1a4ba86"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/lib/db/database_service.dart", "hash": "22a95e38dcebcf3e6203f788253bd8c5"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/drawer.dart", "hash": "2a7bddb3d8610ea23e70920f2e6b2ca2"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/about.dart", "hash": "5aa01cb1f29d05a6fa7abea76e8c12df"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/elevation_overlay.dart", "hash": "5eaed6b1fcf32a11b53e5dcf27ae101c"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/chip_theme.dart", "hash": "61293e5577405d88923d50089bb76120"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/choice_chip.dart", "hash": "5e12d54dcef9e88760c27a2c3ff425ab"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.11.0/LICENSE", "hash": "83228a1ae32476770262d4ff2ac6f984"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/picker.dart", "hash": "324db89ccce9e500bd6d2ab050739b1f"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/text_span.dart", "hash": "81b8ea7416aa896f290f972378fe23fe"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.3.2/LICENSE", "hash": "39062f759b587cf2d49199959513204a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/gestures/velocity_tracker.dart", "hash": "3fa4c89a1c19c846cce6950ff665c20a"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/ink_sparkle.dart", "hash": "7cfa010948b0cbff79028cd5f709de03"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.3.3+1/lib/src/sqflite_import.dart", "hash": "afa8ae229bc41c02a6cd9dcbe10a81e8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/gesture_detector.dart", "hash": "3d4570114c8c1bfce2b55dd272452147"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/animated_icons/data/search_ellipsis.g.dart", "hash": "e822107ed1c00c270f7e9ccfe670576c"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.3.0/lib/src/characters.dart", "hash": "21bf6725b1fc374f03ae5b2cb46bd95b"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/banner.dart", "hash": "3d34df4fbeab20164141c54c02d5ea04"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/cupertino/text_selection_toolbar.dart", "hash": "97dc86c963a4cfad41d3febf7bbebc48"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/mouse_tracker.dart", "hash": "31886f213eb668b416aa4601629a9945"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/material/navigation_bar_theme.dart", "hash": "b12f18fd97ffec06b763749adcd080be"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/iterable_zip.dart", "hash": "df699735e3bcd730f16ce377d562f787"}, {"path": "/Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.18.0/lib/src/comparators.dart", "hash": "d1410f48ac374235aaad55cba40bc4be"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/text_editing.dart", "hash": "a1497040765f0f7199f990aa00af5310"}, {"path": "/Users/<USER>/Documents/pzy/flutter/CollectColors/collect_color/assets/icons/icon_unlike.png", "hash": "f0b3dc3f43c082df311b6f5c54218511"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/services/deferred_component.dart", "hash": "f7b634b150a8381c9b4c03482a0d6e6d"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/widgets/platform_selectable_region_context_menu.dart", "hash": "db1783b3083765425632b2ca451dbbc8"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/painting/paint_utilities.dart", "hash": "0491e1cca60da329c2e03c48abde07c9"}, {"path": "/Users/<USER>/Documents/Flutter/flutter/packages/flutter/lib/src/rendering/sliver_grid.dart", "hash": "76689b5e55b3729a8191b955ef2efb79"}]}